import os
import json
import re
from datetime import datetime

def convert_transcription_to_text(json_file_path):
    """Converte arquivo JSON de transcrição para texto limpo"""
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            transcription_data = json.load(f)
        
        # Extrai informações básicas
        video_id = transcription_data.get('id_video', 'unknown')
        language = transcription_data.get('idioma_transcricao', 'unknown')
        total_segments = transcription_data.get('total_segmentos', 0)
        duration = transcription_data.get('duracao_total_segundos', 0)
        extracted_date = transcription_data.get('extraido_em', 'unknown')
        
        # Extrai os segmentos de transcrição
        segments = transcription_data.get('transcricao', [])
        
        if not segments:
            return None, "Nenhum segmento de transcrição encontrado"
        
        # Converte para texto corrido
        text_parts = []
        
        # Cabeçalho com informações
        text_parts.append(f"=== TRANSCRIÇÃO DO VÍDEO ===")
        text_parts.append(f"ID do Vídeo: {video_id}")
        text_parts.append(f"Idioma: {language}")
        text_parts.append(f"Duração: {duration:.2f} segundos")
        text_parts.append(f"Total de Segmentos: {total_segments}")
        text_parts.append(f"Extraído em: {extracted_date}")
        text_parts.append(f"=" * 50)
        text_parts.append("")
        
        # Processa cada segmento
        for segment in segments:
            start_time = segment.get('start', 0)
            end_time = segment.get('end', 0)
            text = segment.get('text', '').strip()
            
            if text:
                # Formata timestamp
                start_min = int(start_time // 60)
                start_sec = int(start_time % 60)
                timestamp = f"[{start_min:02d}:{start_sec:02d}]"
                
                # Adiciona texto com timestamp
                text_parts.append(f"{timestamp} {text}")
        
        # Junta tudo
        full_text = '\n'.join(text_parts)
        
        return full_text, None
        
    except Exception as e:
        return None, f"Erro ao processar arquivo: {e}"

def load_relevant_videos(channel_folder):
    """Carrega apenas vídeos marcados como relevantes"""
    analyses_file = os.path.join(channel_folder, 'analises', 'videos.json')
    
    if not os.path.exists(analyses_file):
        print("❌ Arquivo de análise de vídeos não encontrado.")
        print("💡 Execute a Etapa 2 (Análise de Vídeos) primeiro!")
        return []
    
    try:
        with open(analyses_file, 'r', encoding='utf-8') as f:
            all_videos = json.load(f)
        
        # Filtra apenas vídeos relevantes
        relevant_videos = [video for video in all_videos if video.get('relevante', False)]
        print(f"📹 Encontrados {len(relevant_videos)} vídeos relevantes para conversão de transcrições")
        return relevant_videos
        
    except Exception as e:
        print(f"⚠️ Erro ao carregar análises de vídeos: {e}")
        return []

def process_all_transcriptions(channel_folder):
    """Processa todas as transcrições dos vídeos relevantes"""
    
    # Carrega vídeos relevantes
    relevant_videos = load_relevant_videos(channel_folder)
    
    if not relevant_videos:
        print("⚠️ Nenhum vídeo relevante encontrado para conversão de transcrições")
        return 0, 0, "Nenhum vídeo relevante encontrado"
    
    # Pastas
    transcriptions_folder = os.path.join(channel_folder, 'transcricoes')
    text_folder = os.path.join(channel_folder, 'transcricoes_texto')
    
    if not os.path.exists(transcriptions_folder):
        return 0, 0, "Pasta de transcrições não encontrada. Execute a Etapa 5 primeiro."
    
    # Cria pasta de texto se não existir
    os.makedirs(text_folder, exist_ok=True)
    
    # Filtra apenas arquivos de vídeos relevantes
    relevant_video_ids = {video.get('id_video') for video in relevant_videos}
    
    # Lista arquivos JSON de transcrições dos vídeos relevantes
    json_files = []
    for filename in os.listdir(transcriptions_folder):
        if filename.endswith('_transcricao.json'):
            video_id = filename.replace('_transcricao.json', '')
            if video_id in relevant_video_ids:
                json_files.append(filename)
    
    if not json_files:
        return 0, 0, "Nenhuma transcrição encontrada para os vídeos relevantes"
    
    print(f"📄 Encontradas {len(json_files)} transcrições de vídeos relevantes para converter")
    
    files_processed = 0
    files_converted = 0
    
    for i, filename in enumerate(json_files):
        json_path = os.path.join(transcriptions_folder, filename)
        
        # Nome do arquivo de texto
        video_id = filename.replace('_transcricao.json', '')
        txt_filename = f"{video_id}_transcricao.txt"
        txt_path = os.path.join(text_folder, txt_filename)
        
        # Verifica se já foi convertido
        if os.path.exists(txt_path):
            print(f"   ⏭️ {i + 1}/{len(json_files)}: {filename} (já convertido)")
            files_processed += 1
            continue
        
        print(f"   📝 {i + 1}/{len(json_files)}: Convertendo {filename}...")
        
        # Converte para texto
        text_content, error = convert_transcription_to_text(json_path)
        
        if text_content:
            # Salva arquivo de texto
            try:
                with open(txt_path, 'w', encoding='utf-8') as f:
                    f.write(text_content)
                
                print(f"      ✅ Convertido: {txt_filename}")
                files_converted += 1
                
            except Exception as e:
                print(f"      ❌ Erro ao salvar: {e}")
        else:
            print(f"      ⚠️ Erro: {error}")
        
        files_processed += 1
    
    print(f"\n📊 Resumo da conversão:")
    print(f"   📄 Arquivos processados: {files_processed}")
    print(f"   ✅ Arquivos convertidos: {files_converted}")
    print(f"   📁 Pasta de texto: {text_folder}")
    
    return files_processed, files_converted, None

class Etapa6ConversaoTranscricaoTexto:
    """Classe para conversão de transcrições dos vídeos relevantes para texto"""
    
    def __init__(self):
        pass
    
    def verificar_transcricoes_relevantes_disponiveis(self, channel_folder):
        """Verifica se há transcrições de vídeos relevantes disponíveis"""
        
        # Verifica se há vídeos relevantes
        relevant_videos = load_relevant_videos(channel_folder)
        if not relevant_videos:
            return False, []
        
        # Verifica pasta de transcrições
        transcriptions_folder = os.path.join(channel_folder, 'transcricoes')
        if not os.path.exists(transcriptions_folder):
            return False, []
        
        # Filtra arquivos de vídeos relevantes
        relevant_video_ids = {video.get('id_video') for video in relevant_videos}
        
        json_files = []
        for filename in os.listdir(transcriptions_folder):
            if filename.endswith('_transcricao.json'):
                video_id = filename.replace('_transcricao.json', '')
                if video_id in relevant_video_ids:
                    json_files.append(filename)
        
        return len(json_files) > 0, json_files
    
    def executar(self, channel_folder):
        """Executa a conversão de transcrições dos vídeos relevantes"""
        print("🎯 ETAPA 6: Conversão de Transcrições para Texto (Vídeos Relevantes)")
        print("=" * 60)
        
        # Verifica se há transcrições de vídeos relevantes disponíveis
        tem_transcricoes, json_files = self.verificar_transcricoes_relevantes_disponiveis(channel_folder)
        
        if not tem_transcricoes:
            return "❌ Nenhuma transcrição de vídeos relevantes encontrada. Execute a Etapa 5 primeiro."
        
        print(f"📄 Encontradas {len(json_files)} transcrições de vídeos relevantes para converter")
        
        # Processa todas as transcrições dos vídeos relevantes
        files_processed, files_converted, error = process_all_transcriptions(channel_folder)
        
        if error:
            return f"❌ Erro: {error}"
        
        if files_processed > 0:
            return f"✅ Conversão concluída! {files_processed} arquivos processados, {files_converted} convertidos para texto."
        else:
            return "⚠️ Nenhum arquivo foi processado."

if __name__ == "__main__":
    # Teste da etapa 6
    etapa6 = Etapa6ConversaoTranscricaoTexto()
    
    # Exemplo de uso
    channel_folder = "Mayra Gaiato _ Desenvolvimento Infantil e Autismo"
    if os.path.exists(channel_folder):
        result = etapa6.executar(channel_folder)
        print(f"\n🎉 Resultado: {result}")
    else:
        print("❌ Pasta do canal não encontrada. Execute as etapas anteriores primeiro.")
