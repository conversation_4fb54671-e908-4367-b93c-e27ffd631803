import os
import json
import re
import yt_dlp
import sys
import time
import random
import signal
from datetime import datetime

class TimeoutException(Exception):
    pass

def timeout_handler(signum, frame):
    raise TimeoutException("Operação expirou")

def with_timeout(seconds, func, *args, **kwargs):
    """Executa função com timeout"""
    if os.name == 'nt':  # Windows
        # No Windows, usamos uma abordagem diferente
        import threading
        result = [None]
        exception = [None]

        def target():
            try:
                result[0] = func(*args, **kwargs)
            except Exception as e:
                exception[0] = e

        thread = threading.Thread(target=target)
        thread.daemon = True
        thread.start()
        thread.join(seconds)

        if thread.is_alive():
            # Thread ainda está rodando, timeout
            raise TimeoutException("Operação expirou")

        if exception[0]:
            raise exception[0]

        return result[0]
    else:  # Unix/Linux
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(seconds)
        try:
            result = func(*args, **kwargs)
            signal.alarm(0)  # Cancela o alarme
            return result
        except TimeoutException:
            raise
        finally:
            signal.alarm(0)

def sanitize_filename(filename):
    """Remove caracteres inválidos do nome do arquivo/pasta"""
    return re.sub(r'[<>:"/\\|?*]', '_', filename).strip()

def save_transcription_incremental(video_id, transcription_data, language, transcriptions_folder):
    """Salva transcrição de forma incremental"""
    try:
        # Cria estrutura de dados completa
        full_data = {
            'extraido_em': datetime.now().isoformat(),
            'id_video': video_id,
            'idioma_transcricao': language,
            'total_segmentos': len(transcription_data),
            'duracao_total_segundos': transcription_data[-1]['end'] if transcription_data else 0,
            'transcricao': transcription_data
        }

        # Salva arquivo
        transcription_file = os.path.join(transcriptions_folder, f"{video_id}_transcricao.json")
        with open(transcription_file, 'w', encoding='utf-8') as f:
            json.dump(full_data, f, ensure_ascii=False, indent=2)

        print(f"      💾 Transcrição salva: {transcription_file}")
        return True

    except Exception as e:
        print(f"      ⚠️ Erro ao salvar transcrição: {e}")
        return False

def extract_video_transcription(video_id, video_title):
    """Extrai transcrição de um vídeo específico"""
    try:
        video_url = f"https://www.youtube.com/watch?v={video_id}"
        
        print(f"      📹 {video_title[:50]}...")
        
        # CONFIGURAÇÕES EXTREMAS - ÚLTIMA TENTATIVA
        base_opts = {
            # Configurações básicas
            'quiet': True,  # Silencioso para reduzir overhead
            'no_warnings': True,
            'skip_download': True,
            'writeinfojson': False,
            'writethumbnail': False,

            # SUBTÍTULOS - CONFIGURAÇÃO MÍNIMA ABSOLUTA
            'writesubtitles': False,
            'writeautomaticsub': True,
            'subtitleslangs': ['pt-BR', 'pt', 'en'],  # Fallback languages
            'subtitlesformat': 'best',    # Aceita qualquer formato
            'listsubtitles': False,

            # REDE - CONFIGURAÇÕES EXTREMAS
            'socket_timeout': 120,        # Timeout muito maior
            'retries': 0,                 # SEM retries
            'fragment_retries': 0,
            'extractor_retries': 0,
            'file_access_retries': 0,

            # BYPASS TOTAL
            'no_check_certificate': True,
            'prefer_insecure': False,
            'geo_bypass': True,

            # USER AGENT PADRÃO
            'http_headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
            },

            # CONFIGURAÇÕES EXPERIMENTAIS
            'concurrent_fragment_downloads': 1,
            'keepvideo': False,
            'noplaylist': True,
        }
        
        # ESTRATÉGIAS ESPECÍFICAS PARA CONTORNAR PO TOKEN
        strategies = [
            ("Web Client", {
                **base_opts,
                'extractor_args': {
                    'youtube': {
                        'player_client': ['web'],
                        'skip': ['dash', 'hls'],
                    }
                }
            }, 90),

            ("iOS Client", {
                **base_opts,
                'extractor_args': {
                    'youtube': {
                        'player_client': ['ios'],
                        'skip': ['dash', 'hls'],
                    }
                }
            }, 60),

            ("TV Client", {
                **base_opts,
                'extractor_args': {
                    'youtube': {
                        'player_client': ['tv_embedded'],
                        'skip': ['dash', 'hls'],
                    }
                }
            }, 45),

            ("Básico", {
                **base_opts,
                'extractor_args': {},
                'socket_timeout': 30,
            }, 30),
        ]

        for strategy_name, opts, timeout in strategies:
            print(f"      🔍 {strategy_name} - Conectando... (timeout: {timeout}s)")
            start_time = time.time()

            try:
                with yt_dlp.YoutubeDL(opts) as ydl:
                    info = with_timeout(timeout, ydl.extract_info, video_url, download=False)
                    connect_time = time.time() - start_time
                    print(f"      ✅ {strategy_name} funcionou! Conectado em {connect_time:.1f}s")
                    break  # Sucesso, sai do loop

            except TimeoutException:
                print(f"      ⏰ {strategy_name} - Timeout ({timeout}s)")
                if strategy_name == "Básico":  # Última tentativa
                    print(f"      ❌ Todas as estratégias falharam")
                    return [], 'timeout'
                continue  # Tenta próxima estratégia

            except Exception as e:
                error_msg = str(e)[:80]
                print(f"      ❌ {strategy_name} - Erro: {error_msg}...")
                if strategy_name == "Básico":  # Última tentativa
                    return [], 'error'
                continue  # Tenta próxima estratégia
            
            # Verifica se há legendas automáticas disponíveis
            automatic_captions = info.get('automatic_captions', {})
            
            if not automatic_captions:
                print(f"      ⚠️ Nenhuma legenda automática disponível")
                return [], 'unknown'
            
            # Prioriza português, depois inglês
            language_priority = ['pt', 'pt-BR', 'en']
            selected_language = None
            selected_captions = None
            
            for lang in language_priority:
                if lang in automatic_captions:
                    selected_language = lang
                    selected_captions = automatic_captions[lang]
                    break
            
            if not selected_captions:
                # Pega a primeira língua disponível
                selected_language = list(automatic_captions.keys())[0]
                selected_captions = automatic_captions[selected_language]
            
            print(f"      🌐 Idioma selecionado: {selected_language}")
            
            # Procura por formato JSON3 (mais detalhado)
            json3_url = None
            for caption_format in selected_captions:
                if caption_format.get('ext') == 'json3':
                    json3_url = caption_format.get('url')
                    break
            
            if not json3_url:
                print(f"      ⚠️ Formato JSON3 não disponível")
                return [], selected_language
            
            # Baixa e processa a transcrição
            print(f"      📥 Baixando transcrição...")
            
            import urllib.request
            import json as json_module
            
            with urllib.request.urlopen(json3_url) as response:
                caption_data = json_module.loads(response.read().decode('utf-8'))
            
            # Processa os eventos da transcrição
            transcription_segments = []
            
            if 'events' in caption_data:
                for event in caption_data['events']:
                    if 'segs' in event and event.get('tStartMs') is not None:
                        start_time = event['tStartMs'] / 1000.0  # Converte para segundos
                        duration = event.get('dDurationMs', 0) / 1000.0
                        end_time = start_time + duration
                        
                        # Junta todos os segmentos de texto
                        text_parts = []
                        for seg in event['segs']:
                            if 'utf8' in seg:
                                text_parts.append(seg['utf8'])
                        
                        text = ''.join(text_parts).strip()
                        
                        if text:  # Só adiciona se houver texto
                            transcription_segments.append({
                                'start': round(start_time, 2),
                                'end': round(end_time, 2),
                                'duration': round(duration, 2),
                                'text': text
                            })
            
            if transcription_segments:
                print(f"      ✅ {len(transcription_segments)} segmentos extraídos")
                return transcription_segments, selected_language
            else:
                print(f"      ⚠️ Nenhum segmento de texto encontrado")
                return [], selected_language

    except Exception as e:
        print(f"      ❌ Erro ao extrair transcrição: {e}")
        return [], 'unknown'

    # Fallback caso nenhum caminho anterior retorne
    print(f"      ⚠️ Nenhuma transcrição encontrada")
    return [], 'unknown'

def load_relevant_videos(channel_folder):
    """Carrega apenas vídeos marcados como relevantes"""
    analyses_file = os.path.join(channel_folder, 'analises', 'videos.json')
    
    if not os.path.exists(analyses_file):
        print("❌ Arquivo de análise de vídeos não encontrado.")
        print("💡 Execute a Etapa 2 (Análise de Vídeos) primeiro!")
        return []
    
    try:
        with open(analyses_file, 'r', encoding='utf-8') as f:
            all_videos = json.load(f)
        
        # Filtra apenas vídeos relevantes
        relevant_videos = [video for video in all_videos if video.get('relevante', False)]
        print(f"📹 Encontrados {len(relevant_videos)} vídeos relevantes para extração de transcrições")
        return relevant_videos
        
    except Exception as e:
        print(f"⚠️ Erro ao carregar análises de vídeos: {e}")
        return []

def extract_all_transcriptions(videos_data, channel_folder):
    """Extrai transcrições apenas dos vídeos relevantes"""
    
    # Carrega vídeos relevantes
    relevant_videos = load_relevant_videos(channel_folder)
    
    if not relevant_videos:
        print("⚠️ Nenhum vídeo relevante encontrado para extração de transcrições")
        return 0, 0
    
    print("📝 Iniciando extração de transcrições dos vídeos RELEVANTES...")
    print(f"   📊 Total de vídeos relevantes para processar: {len(relevant_videos)}")
    print(f"   🎯 Extraindo transcrições apenas dos vídeos marcados como relevantes")
    
    # Cria pasta de transcrições
    transcriptions_folder = os.path.join(channel_folder, 'transcricoes')
    os.makedirs(transcriptions_folder, exist_ok=True)
    print(f"   📁 Pasta de transcrições: {transcriptions_folder}")
    
    # Verifica quais vídeos já têm transcrições extraídas
    videos_processados = 0
    videos_com_transcricao = 0
    videos_pulados = 0
    videos_sem_transcricao = 0
    timeouts_consecutivos = 0  # Contador de timeouts consecutivos
    
    for i, video in enumerate(relevant_videos):
        video_id = video.get('id_video')
        video_title = video.get('titulo_video', 'Título não disponível')
        
        if not video_id:
            continue
        
        # Verifica se já existe arquivo de transcrição
        transcription_file = os.path.join(transcriptions_folder, f"{video_id}_transcricao.json")
        if os.path.exists(transcription_file):
            videos_pulados += 1
            print(f"   ⏭️ Vídeo {i + 1}/{len(relevant_videos)}: {video_title[:50]}... (transcrição já extraída)")
            continue
        
        print(f"   🎬 Processando vídeo {i + 1}/{len(relevant_videos)}")
        
        # Extrai transcrição do vídeo
        result = extract_video_transcription(video_id, video_title)

        # Verificação de segurança
        if result is None or not isinstance(result, tuple) or len(result) != 2:
            print(f"      ❌ Erro: Função retornou resultado inválido")
            transcription_data, language = [], 'unknown'
        else:
            transcription_data, language = result

        # SISTEMA DE PAUSA INTELIGENTE PARA TIMEOUTS
        if not transcription_data and language == 'unknown':
            timeouts_consecutivos += 1
            print(f"      ⚠️ Possível timeout/erro - Consecutivos: {timeouts_consecutivos}")

            # Se muitos timeouts consecutivos, faz uma pausa longa
            if timeouts_consecutivos >= 3:
                pause_time = 300  # 5 minutos de pausa
                print(f"      🛑 MUITOS ERROS CONSECUTIVOS! Pausando por {pause_time//60} minutos...")
                print(f"      💡 YouTube pode estar bloqueando temporariamente")
                print(f"      ⏰ Aguarde... ({pause_time}s)")
                time.sleep(pause_time)
                timeouts_consecutivos = 0  # Reset contador
                print(f"      🔄 Retomando extração...")
            elif timeouts_consecutivos >= 2:
                pause_time = 60  # 1 minuto após 2 erros
                print(f"      ⚠️ Pausando por {pause_time}s após {timeouts_consecutivos} erros...")
                time.sleep(pause_time)
        else:
            timeouts_consecutivos = 0  # Reset se teve sucesso

        # Salva transcrição
        if transcription_data:
            save_transcription_incremental(video_id, transcription_data, language, transcriptions_folder)
            videos_com_transcricao += 1
        else:
            videos_sem_transcricao += 1
        
        videos_processados += 1
        
        # Delay inteligente entre vídeos para evitar rate limiting
        if i < len(relevant_videos) - 1:  # Não faz delay no último vídeo
            # Delay baseado no resultado anterior e taxa de sucesso
            success_rate = videos_com_transcricao / max(videos_processados, 1)

            if success_rate > 0.7:  # Taxa de sucesso > 70%
                delay = random.uniform(2, 4)    # Delay menor
            elif success_rate > 0.3:  # Taxa de sucesso > 30%
                delay = random.uniform(5, 8)    # Delay médio
            else:  # Taxa de sucesso baixa
                delay = random.uniform(10, 15)  # Delay maior

            print(f"      ⏳ Aguardando {delay:.1f}s (taxa sucesso: {success_rate:.1%})...")
            time.sleep(delay)

    print(f"\n📊 Resumo da extração:")
    print(f"   ✅ Vídeos processados: {videos_processados}")
    print(f"   📝 Vídeos com transcrição: {videos_com_transcricao}")
    print(f"   ❌ Vídeos sem transcrição: {videos_sem_transcricao}")
    print(f"   ⏭️ Vídeos pulados (já extraídos): {videos_pulados}")
    
    return videos_processados, videos_com_transcricao

class Etapa5ExtracaoTranscricoes:
    """Classe para extração de transcrições dos vídeos relevantes"""
    
    def __init__(self):
        pass
    
    def verificar_videos_relevantes_disponiveis(self, channel_folder):
        """Verifica se há vídeos relevantes disponíveis para extrair transcrições"""
        analyses_file = os.path.join(channel_folder, 'analises', 'videos.json')
        
        if not os.path.exists(analyses_file):
            return False, []
        
        try:
            with open(analyses_file, 'r', encoding='utf-8') as f:
                all_videos = json.load(f)
            
            # Filtra apenas vídeos relevantes
            relevant_videos = [video for video in all_videos if video.get('relevante', False)]
            return True, relevant_videos
        except Exception as e:
            print(f"⚠️ Erro ao carregar análises de vídeos: {e}")
            return False, []
    
    def executar(self, channel_folder):
        """Executa a extração de transcrições dos vídeos relevantes"""
        print("🎯 ETAPA 5: Extração de Transcrições dos Vídeos Relevantes")
        print("=" * 60)
        
        # Verifica se há vídeos relevantes disponíveis
        tem_videos, relevant_videos = self.verificar_videos_relevantes_disponiveis(channel_folder)
        
        if not tem_videos:
            return "❌ Nenhuma análise de vídeos encontrada. Execute a Etapa 2 (Análise de Vídeos) primeiro."
        
        if not relevant_videos:
            return "⚠️ Nenhum vídeo foi marcado como relevante."
        
        # Extrai transcrições dos vídeos relevantes
        videos_processados, videos_com_transcricao = extract_all_transcriptions(
            relevant_videos, channel_folder
        )
        
        # Relatório final
        if videos_processados > 0:
            return f"✅ Extração concluída! {videos_processados} vídeos processados, {videos_com_transcricao} com transcrições extraídas."
        else:
            return "⚠️ Nenhum vídeo foi processado."

if __name__ == "__main__":
    # Teste da etapa 5
    etapa5 = Etapa5ExtracaoTranscricoes()
    
    # Exemplo de uso
    channel_folder = "Mayra Gaiato _ Desenvolvimento Infantil e Autismo"
    if os.path.exists(channel_folder):
        result = etapa5.executar(channel_folder)
        print(f"\n🎉 Resultado: {result}")
    else:
        print("❌ Pasta do canal não encontrada. Execute as etapas anteriores primeiro.")
