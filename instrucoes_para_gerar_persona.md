## 🧠 GUIA DE IMPLEMENTAÇÃO – PLANEJAMENTO DO SCRIPT POR ETAPA

Este é um passo a passo com instruções técnicas que você pode usar para **pedir para a IA ou dev criar os scripts** com precisão.

---

### ✅ 1. **Carregamento do arquivo JSON**

**Objetivo:** Ler o arquivo `/analises/comentarios.json` contendo milhares de objetos (comentários).

**Instruções para criação do script:**

* Usar `json.load()` se o arquivo for pequeno o suficiente para caber na RAM.
* Se o arquivo for muito grande (acima de 500MB), usar `ijson` para leitura incremental.
* Validar se o conteúdo é uma **lista de dicionários**.
* Contar e imprimir quantos comentários existem no total.

```plain
Entrada: comentarios.json
Saída esperada: uma lista de comentários (dicionários)
```

---

### ✅ 2. **Filtrar apenas os comentários úteis**

**Objetivo:** Separar os comentários que têm `"comentario_util": true`.

**Instruções para a IA criar o script:**

* Iterar pela lista de comentários carregados.
* Armazenar somente os que têm `"comentario_util" == true` em uma nova lista chamada `comentarios_uteis`.
* Imprimir quantos comentários úteis foram encontrados.

```plain
Entrada: lista de comentários
Filtro: comentario["comentario_util"] == true
Saída: lista filtrada chamada comentarios_uteis
```

---

### ✅ 3. **Agrupar e contar campos-chave**

**Objetivo:** Agrupar informações estratégicas para análise de persona.

**Campos a processar (com tipo e agrupamento):**

| Campo                     | Tipo de dado     | Ação                                                              |
| ------------------------- | ---------------- | ----------------------------------------------------------------- |
| `"categoria"`             | string           | Contar frequência                                                 |
| `"medos"`                 | lista de strings | Agrupar todos os medos e contar ocorrências                       |
| `"dificuldades"`          | lista de strings | Agrupar e contar                                                  |
| `"valores"`               | lista de strings | Agrupar e contar                                                  |
| `"sonhos"`                | lista de strings | Agrupar e contar                                                  |
| `"emocao_principal"`      | string           | Contar por frequência                                             |
| `"linguagem"`             | lista de strings | Juntar tudo e contar repetições                                   |
| `"motivacao"`             | string           | Armazenar para posterior análise qualitativa                      |
| `"resumo"`                | string           | Coletar uma amostra aleatória ou os 10 primeiros para o documento |
| `"ideias_conteudo"`       | lista de strings | Agrupar por tema e contar sugestões                               |
| `"oportunidades_negocio"` | lista de strings | Agrupar e contar sugestões                                        |

**Instruções ao script:**

* Para cada campo de lista, usar `collections.Counter` para contar todos os valores (normalizar para `.lower().strip()`).
* Armazenar todos os dados em dicionários separados para gerar os blocos de texto depois.

```plain
Exemplo:
medos_counter = Counter()
Para cada comentario em comentarios_uteis:
    Para cada medo em comentario["medos"]:
        medos_counter[medo.lower().strip()] += 1
```

---

### ✅ 4. **Síntese dos dados (geração dos blocos)**

**Objetivo:** Gerar blocos de texto que podem ser colados no campo semântico final.

**Blocos a gerar:**

* Top 100 Categorias mais citadas
* Top 100 Medos
* Top 100 Dificuldades
* Top 100 Valores
* Top 100 Sonhos
* Frequência das Emoções
* 100 expressões reais mais frequentes
* 100 resumos mais representativos
* Top 100 ideias de produto digital
* Top 100 ideias de conteúdo

**Instruções para a IA:**

* Criar uma função `gerar_bloco_top_n(counter_obj, titulo, n)` que formate os resultados.
* Salvar tudo em strings organizadas por seção.

```plain
Bloco de exemplo:
⚠️ Medos mais frequentes:
- não saber como ajudar o filho: 258
- ser julgada pela família: 189
- não conseguir lidar com comportamentos: 174
```

---

### ✅ 5. **Exportar o Campo Semântico como Documento**

**Objetivo:** Criar um arquivo `.docx` com as seções organizadas.

**Sugestões de estrutura:**

```markdown
# 📘 Campo Semântico do Público – Projeto Autismo

## 1. Perfil da Persona
...

## 2. Dores e Desafios
...

## 3. Medos mais Frequentes
...

## 4. Sonhos e Aspirações
...

## 5. Emoções Dominantes
...

## 6. Valores
...

## 7. Linguagem Real
...

## 8. Exemplos de Resumos
...

## 9. Ideias de Conteúdo com Alto Potencial
...

## 10. Oportunidades de Produtos Digitais
...
```

**Instruções para a IA criar o script:**

* Concatenar todos os blocos formatados em uma string única.
* Usar `python-docx` para exportar em Word, com cabeçalhos e listas.

---

### ✅ 6. Exportação de dados estruturados

* Exportar cada agrupamento como `.docx`:

  * `medos.docx`
  * `dificuldades.docx`
  * `linguagem.docx`
  etc...
* Uma linha por item + contagem.