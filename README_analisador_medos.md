# 🎯 Analisador Estratégico de Medos da Persona

Este script usa IA (OpenAI GPT-4o-mini) para transformar dados brutos de medos em insights estratégicos para marketing, copywriting e criação de produtos digitais.

## 🚀 Funcionalidades

- **Processamento em Lotes**: Divide dados grandes em lotes menores para respeitar limites da API
- **Análise Estratégica**: Transforma medos em insights acionáveis para marketing
- **Consolidação Inteligente**: Combina análises parciais em documento estratégico final
- **Backup Automático**: Salva cada lote processado como backup
- **Formato Otimizado**: Saída em Markdown com emojis para fácil leitura

## 📋 Pré-requisitos

### 1. Chave da API OpenAI
1. Acesse [OpenAI API Keys](https://platform.openai.com/api-keys)
2. Faça login e crie uma nova chave secreta
3. Copie a chave (começa com `sk-`)

### 2. Configuração do Ambiente
```bash
# Copie o arquivo de exemplo
copy .env.exemplo .env

# Edite o arquivo .env e adicione sua chave:
OPENAI_API_KEY=sk-sua_chave_aqui
```

### 3. Instalar Dependências
```bash
pip install openai python-dotenv
```

## 🎯 Como Usar

### Execução Básica
```bash
python analisador_persona_medos.py
```

### O que o Script Faz

1. **Carrega o Prompt**: Lê `prompt_analise_persona_medos.md`
2. **Carrega os Dados**: Lê `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/dados_persona_medos.txt`
3. **Divide em Lotes**: Separa os 1830+ medos em lotes de 80 itens
4. **Processa com IA**: Envia cada lote para análise estratégica
5. **Consolida**: Combina todas as análises em documento final
6. **Salva Resultados**: Gera arquivos organizados na pasta persona

## 📁 Arquivos Gerados

### Arquivo Principal
- `analise_estrategica_medos.md` - Documento estratégico consolidado

### Arquivos de Backup
- `analise_medos_lote_1.md` - Análise do primeiro lote
- `analise_medos_lote_2.md` - Análise do segundo lote
- ... (um arquivo por lote processado)

## 📊 Estrutura da Análise

O documento final contém:

### 🔍 Agrupamento Temático
- Medos organizados por temas centrais
- Frequência total por tema
- Variações mais comuns

### 🧠 Perfil Psicológico
- O que cada medo revela sobre a persona
- Emoções associadas (culpa, vergonha, medo)
- Gatilhos emocionais específicos

### ⚡ Impactos Comportamentais
- Como os medos influenciam decisões
- Padrões de consumo de conteúdo
- Comportamentos de busca/evitação

### 💬 Arsenal Estratégico
- **Headlines de Impacto**: Frases para copys e títulos
- **Oportunidades de Produto**: Ideias de produtos digitais
- **Conteúdo Estratégico**: Vídeos e posts baseados nos medos

## ⚙️ Configurações Avançadas

### Ajustar Tamanho dos Lotes
```python
# No arquivo analisador_persona_medos.py, linha final:
resultado = analisador.executar_analise_completa(tamanho_lote=50)  # Lotes menores
```

### Trocar Modelo da IA
```python
# Na função processar_lote(), altere:
model="gpt-4o"  # Modelo mais avançado (mais caro)
# ou
model="gpt-3.5-turbo"  # Modelo mais barato
```

## 💰 Custos Estimados

Com GPT-4o-mini (modelo padrão):
- **1830 medos** ≈ 23 lotes de 80 itens
- **Custo estimado**: $2-5 USD por execução completa
- **Tokens por lote**: ~3000-4000 tokens

## 🔧 Solução de Problemas

### Erro: "OPENAI_API_KEY não encontrada"
```bash
# Verifique se o arquivo .env existe e contém:
OPENAI_API_KEY=sk-sua_chave_aqui
```

### Erro: "Rate limit exceeded"
- Aguarde alguns minutos e tente novamente
- Reduza o tamanho dos lotes para 50 ou 30 itens

### Erro: "Arquivo não encontrado"
- Execute primeiro o `gerador_persona.py` para criar os dados
- Verifique se a pasta `persona` existe no projeto

## 📈 Exemplo de Saída

```markdown
# 🎯 ANÁLISE ESTRATÉGICA DOS MEDOS

## 🔍 TEMA: COMPETÊNCIA PARENTAL
**Frequência Total**: 234 ocorrências

### Medos Principais:
- não saber como agir: 59
- não conseguir ajudar meu filho: 43
- não ser uma boa mãe: 10

### 🧠 Perfil Psicológico:
Revela insegurança profunda sobre capacidade parental...

### 💬 Headlines de Impacto:
- "Você não está falhando como mãe/pai"
- "O guia que toda mãe de autista precisa"
```

## 🎯 Próximos Passos

Após gerar a análise:
1. **Revise os insights** gerados
2. **Adapte para sua estratégia** específica
3. **Teste headlines** em anúncios pequenos
4. **Desenvolva produtos** baseados nos medos identificados
5. **Crie conteúdos** que abordem os medos principais

## ⚠️ Importante

- **Ética**: Use os insights com responsabilidade e empatia
- **Privacidade**: Os dados são anônimos, mas representam pessoas reais
- **Teste**: Sempre teste estratégias antes de implementar em larga escala
