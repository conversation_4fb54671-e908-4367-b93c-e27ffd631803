import os
import json
import re
import yt_dlp
import sys
import time
import random
from datetime import datetime

def sanitize_filename(filename):
    """Remove caracteres inválidos do nome do arquivo/pasta"""
    return re.sub(r'[<>:"/\\|?*]', '_', filename).strip()

def organize_comments_with_replies(comments):
    """Organiza comentários e suas respostas corretamente"""

    # Separa comentários principais das respostas
    main_comments = {}
    replies_by_parent = {}

    for comment in comments:
        parent = comment.get('parent', 'root')

        if parent == 'root':
            # É um comentário principal
            main_comments[comment['id']] = comment
        else:
            # É uma resposta
            if parent not in replies_by_parent:
                replies_by_parent[parent] = []
            replies_by_parent[parent].append(comment)

    # Organiza comentários com suas respostas
    organized_comments = []

    for comment_id, comment in main_comments.items():
        # Formata timestamp (corrige se for futuro)
        timestamp = comment.get('timestamp')
        if timestamp:
            # Se o timestamp for maior que agora, é um erro - usar None
            current_timestamp = datetime.now().timestamp()
            if timestamp > current_timestamp:
                formatted_time = None
                timestamp = None
            else:
                formatted_time = datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M:%S")
        else:
            formatted_time = None

        # Processa respostas
        replies = replies_by_parent.get(comment_id, [])
        formatted_replies = []

        for reply in replies:
            reply_timestamp = reply.get('timestamp')
            if reply_timestamp:
                current_timestamp = datetime.now().timestamp()
                if reply_timestamp > current_timestamp:
                    reply_formatted_time = None
                    reply_timestamp = None
                else:
                    reply_formatted_time = datetime.fromtimestamp(reply_timestamp).strftime("%Y-%m-%d %H:%M:%S")
            else:
                reply_formatted_time = None

            formatted_replies.append({
                'id_resposta': reply['id'],
                'texto_resposta': reply.get('text', ''),
                'autor_resposta': reply.get('author', ''),
                'curtidas_resposta': reply.get('like_count', 0),
                'data_resposta': reply_formatted_time,
                'timestamp_resposta': reply_timestamp,
                'autor_verificado': reply.get('author_is_verified', False),
                'canal_autor': reply.get('author_url', ''),
                'eh_criador': reply.get('author_is_uploader', False),
                'miniatura_autor': reply.get('author_thumbnail', '')
            })

        # Monta comentário principal com respostas
        organized_comment = {
            'id_comentario': comment['id'],
            'texto_comentario': comment.get('text', ''),
            'autor_comentario': comment.get('author', ''),
            'curtidas_comentario': comment.get('like_count', 0),
            'data_comentario': formatted_time,
            'timestamp_comentario': timestamp,
            'autor_verificado': comment.get('author_is_verified', False),
            'canal_autor': comment.get('author_url', ''),
            'eh_criador': comment.get('author_is_uploader', False),
            'fixado': comment.get('is_pinned', False),
            'favoritado': comment.get('is_favorited', False),
            'miniatura_autor': comment.get('author_thumbnail', ''),
            'total_respostas': len(formatted_replies),
            'respostas': formatted_replies
        }

        organized_comments.append(organized_comment)

    return organized_comments

def save_comments_incremental(video_id, comments_data, comments_folder):
    """Salva comentários de forma incremental"""
    try:
        # Cria estrutura de dados completa
        full_data = {
            'extraido_em': datetime.now().isoformat(),
            'id_video': video_id,
            'total_comentarios': len(comments_data),
            'comentarios': comments_data
        }

        # Salva arquivo
        comments_file = os.path.join(comments_folder, f"{video_id}_comentarios.json")
        with open(comments_file, 'w', encoding='utf-8') as f:
            json.dump(full_data, f, ensure_ascii=False, indent=2)

        print(f"      💾 Comentários salvos: {comments_file}")
        return True

    except Exception as e:
        print(f"      ⚠️ Erro ao salvar comentários: {e}")
        return False

def extract_video_comments(video_id, video_title):
    """Extrai comentários de um vídeo específico"""
    try:
        video_url = f"https://www.youtube.com/watch?v={video_id}"
        
        print(f"      📹 {video_title[:50]}...")
        
        # Configurações do yt-dlp para comentários
        ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'writecomments': True,
            'getcomments': True,
            'extract_flat': False,
            'skip_download': True,
            'writeinfojson': False,
            'writethumbnail': False,
            'writesubtitles': False,
            'writeautomaticsub': False,
            
            # Configurações de comentários
            'max_comments': None,  # Extrai TODOS os comentários
            'comment_sort': 'top',  # Ordena por mais relevantes primeiro
            
            # Configurações de rede
            'socket_timeout': 30,
            'retries': 3,
            'fragment_retries': 3,
            'extractor_retries': 3,
            
            # Headers para evitar bloqueios
            'http_headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            },
            
            # Configurações de proxy/bypass
            'geo_bypass': True,
            'nocheckcertificate': True,
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            print(f"      🔍 Conectando ao vídeo...")
            info = ydl.extract_info(video_url, download=False)
            
            if 'comments' in info and info['comments']:
                comments = info['comments']
                print(f"      📊 Encontrados {len(comments)} comentários brutos")

                # Organiza comentários com suas respostas
                comments_data = organize_comments_with_replies(comments)

                print(f"      ✅ {len(comments_data)} comentários principais organizados")

                # Mostra estatísticas
                total_replies = sum(comment['total_respostas'] for comment in comments_data)
                print(f"      📝 Total de respostas: {total_replies}")

                return comments_data
            else:
                print(f"      ℹ️ Nenhum comentário encontrado ou comentários desabilitados")
                return []

    except Exception as e:
        print(f"      ❌ Erro ao extrair comentários: {e}")
        return []

def load_relevant_videos(channel_folder):
    """Carrega apenas vídeos marcados como relevantes"""
    analyses_file = os.path.join(channel_folder, 'analises', 'videos.json')
    
    if not os.path.exists(analyses_file):
        print("❌ Arquivo de análise de vídeos não encontrado.")
        print("💡 Execute a Etapa 2 (Análise de Vídeos) primeiro!")
        return []
    
    try:
        with open(analyses_file, 'r', encoding='utf-8') as f:
            all_videos = json.load(f)
        
        # Filtra apenas vídeos relevantes
        relevant_videos = [video for video in all_videos if video.get('relevante', False)]
        print(f"📹 Encontrados {len(relevant_videos)} vídeos relevantes para extração de comentários")
        return relevant_videos
        
    except Exception as e:
        print(f"⚠️ Erro ao carregar análises de vídeos: {e}")
        return []

def extract_all_comments(videos_data, channel_folder):
    """Extrai comentários apenas dos vídeos relevantes"""
    
    # Carrega vídeos relevantes
    relevant_videos = load_relevant_videos(channel_folder)
    
    if not relevant_videos:
        print("⚠️ Nenhum vídeo relevante encontrado para extração de comentários")
        return 0, 0

    print("💬 Iniciando extração de comentários dos vídeos RELEVANTES...")
    print(f"   📊 Total de vídeos relevantes para processar: {len(relevant_videos)}")
    print(f"   🎯 Extraindo comentários apenas dos vídeos marcados como relevantes")

    # Cria pasta de comentários
    comments_folder = os.path.join(channel_folder, 'comentarios')
    os.makedirs(comments_folder, exist_ok=True)
    print(f"   📁 Pasta de comentários: {comments_folder}")
    
    # Verifica quais vídeos já têm comentários extraídos
    videos_processados = 0
    videos_com_comentarios = 0
    videos_pulados = 0
    
    for i, video in enumerate(relevant_videos):
        video_id = video.get('id_video')
        video_title = video.get('titulo_video', 'Título não disponível')
        
        if not video_id:
            continue
        
        # Verifica se já existe arquivo de comentários
        comments_file = os.path.join(comments_folder, f"{video_id}_comentarios.json")
        if os.path.exists(comments_file):
            videos_pulados += 1
            print(f"   ⏭️ Vídeo {i + 1}/{len(relevant_videos)}: {video_title[:50]}... (comentários já extraídos)")
            continue
        
        print(f"   🎬 Processando vídeo {i + 1}/{len(relevant_videos)}")
        
        # Extrai comentários do vídeo
        comments_data = extract_video_comments(video_id, video_title)
        
        # Salva comentários
        if comments_data:
            save_comments_incremental(video_id, comments_data, comments_folder)
            videos_com_comentarios += 1
        else:
            print(f"      ⚠️ Nenhum comentário extraído")
        
        videos_processados += 1
        
        # Delay entre vídeos para evitar rate limiting
        if i < len(relevant_videos) - 1:  # Não faz delay no último vídeo
            delay = random.randint(5, 10)  # Delay aleatório entre 5-10 segundos
            print(f"      ⏳ Aguardando {delay}s antes do próximo vídeo...")
            time.sleep(delay)

    print(f"\n📊 Resumo da extração:")
    print(f"   ✅ Vídeos processados: {videos_processados}")
    print(f"   💬 Vídeos com comentários: {videos_com_comentarios}")
    print(f"   ⏭️ Vídeos pulados (já extraídos): {videos_pulados}")
    
    return videos_processados, videos_com_comentarios

class Etapa3ExtracaoComentarios:
    """Classe para extração de comentários dos vídeos relevantes"""
    
    def __init__(self):
        pass
    
    def verificar_videos_relevantes_disponiveis(self, channel_folder):
        """Verifica se há vídeos relevantes disponíveis para extrair comentários"""
        analyses_file = os.path.join(channel_folder, 'analises', 'videos.json')
        
        if not os.path.exists(analyses_file):
            return False, []
        
        try:
            with open(analyses_file, 'r', encoding='utf-8') as f:
                all_videos = json.load(f)
            
            # Filtra apenas vídeos relevantes
            relevant_videos = [video for video in all_videos if video.get('relevante', False)]
            return True, relevant_videos
        except Exception as e:
            print(f"⚠️ Erro ao carregar análises de vídeos: {e}")
            return False, []
    
    def executar(self, channel_folder):
        """Executa a extração de comentários dos vídeos relevantes"""
        print("🎯 ETAPA 3: Extração de Comentários dos Vídeos Relevantes")
        print("=" * 60)
        
        # Verifica se há vídeos relevantes disponíveis
        tem_videos, relevant_videos = self.verificar_videos_relevantes_disponiveis(channel_folder)
        
        if not tem_videos:
            return "❌ Nenhuma análise de vídeos encontrada. Execute a Etapa 2 (Análise de Vídeos) primeiro."
        
        if not relevant_videos:
            return "⚠️ Nenhum vídeo foi marcado como relevante."
        
        # Extrai comentários dos vídeos relevantes
        videos_processados, videos_com_comentarios = extract_all_comments(
            relevant_videos, channel_folder
        )
        
        # Relatório final
        if videos_processados > 0:
            return f"✅ Extração concluída! {videos_processados} vídeos processados, {videos_com_comentarios} com comentários extraídos."
        else:
            return "⚠️ Nenhum vídeo foi processado."

if __name__ == "__main__":
    # Teste da etapa 3
    etapa3 = Etapa3ExtracaoComentarios()
    
    # Exemplo de uso
    channel_folder = "Mayra Gaiato _ Desenvolvimento Infantil e Autismo"
    if os.path.exists(channel_folder):
        result = etapa3.executar(channel_folder)
        print(f"\n🎉 Resultado: {result}")
    else:
        print("❌ Pasta do canal não encontrada. Execute as etapas anteriores primeiro.")
