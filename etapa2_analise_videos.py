import json
import os
import asyncio
import time
from openai import AsyncOpenAI
from dotenv import load_dotenv
from typing import List, Dict

# Carrega variáveis de ambiente
load_dotenv()

class AnalisadorVideosRapido:
    """Analisador de vídeos com processamento concorrente para máxima velocidade"""
    
    def __init__(self, max_concurrent_requests=10, requests_per_minute=500):
        self.client = AsyncOpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.max_concurrent = max_concurrent_requests
        self.requests_per_minute = requests_per_minute
        self.semaphore = asyncio.Semaphore(max_concurrent_requests)
        self.request_times = []
        
    def load_prompt_template(self):
        """Carrega template do prompt"""
        try:
            with open('prompt_analise_video.md', 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            print(f"⚠️ Erro ao carregar prompt: {e}")
            return None
    
    async def rate_limit_wait(self):
        """Controla rate limiting de forma inteligente"""
        now = time.time()
        
        # Remove requisições antigas (mais de 1 minuto)
        self.request_times = [t for t in self.request_times if now - t < 60]
        
        # Se estamos próximos do limite, aguarda
        if len(self.request_times) >= self.requests_per_minute:
            wait_time = 60 - (now - self.request_times[0])
            if wait_time > 0:
                await asyncio.sleep(wait_time)
                self.request_times = []
        
        self.request_times.append(now)
    
    async def analyze_single_video(self, video_data: Dict, prompt_template: str) -> Dict:
        """Analisa um único vídeo de forma assíncrona"""
        
        async with self.semaphore:  # Limita concorrência
            await self.rate_limit_wait()  # Controla rate limiting
            
            try:
                video_id = video_data.get('id_video')
                video_title = video_data.get('titulo', '')
                
                # Monta prompt específico para este vídeo
                analysis_prompt = f"""
{prompt_template}

Agora analise este vídeo:

ID do vídeo: {video_id}
Título: {video_title}

Responda APENAS com um JSON válido no formato especificado no prompt.
"""
                
                # Faz requisição assíncrona
                response = await self.client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[
                        {
                            "role": "system", 
                            "content": "Você é uma especialista em análise de audiência. Responda APENAS com JSON válido."
                        },
                        {
                            "role": "user", 
                            "content": analysis_prompt
                        }
                    ],
                    temperature=0.3,
                    max_tokens=500
                )
                
                # Processa resposta
                ai_response = response.choices[0].message.content.strip()
                analysis_result = json.loads(ai_response)
                
                return analysis_result
                
            except json.JSONDecodeError as e:
                print(f"⚠️ Erro JSON para vídeo {video_id}: {e}")
                return None
            except Exception as e:
                print(f"❌ Erro na análise do vídeo {video_id}: {e}")
                return None

    async def analyze_batch_videos(self, videos_to_analyze: List[Dict], prompt_template: str) -> List[Dict]:
        """Analisa um lote de vídeos de forma concorrente"""
        
        print(f"🚀 Analisando {len(videos_to_analyze)} vídeos concorrentemente...")
        start_time = time.time()
        
        # Cria tasks para todos os vídeos
        tasks = []
        for video in videos_to_analyze:
            task = self.analyze_single_video(video, prompt_template)
            tasks.append(task)
        
        # Executa todas as análises concorrentemente
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filtra resultados válidos
        valid_results = []
        for result in results:
            if isinstance(result, dict):
                valid_results.append(result)
            elif isinstance(result, Exception):
                print(f"⚠️ Erro em análise: {result}")
        
        elapsed = time.time() - start_time
        print(f"✅ {len(valid_results)}/{len(videos_to_analyze)} vídeos analisados em {elapsed:.1f}s")
        
        return valid_results
    
    async def process_all_videos(self, videos_data: List[Dict], channel_folder: str):
        """Processa todos os vídeos"""
        
        prompt_template = self.load_prompt_template()
        if not prompt_template:
            return [], 0
        
        # Carrega análises existentes
        analyses_folder = os.path.join(channel_folder, 'analises')
        existing_analyses = self.load_existing_analyses(analyses_folder)
        analyzed_video_ids = {analysis.get('id_video') for analysis in existing_analyses}
        
        print(f"📊 Análises já existentes: {len(existing_analyses)}")
        
        # Filtra vídeos que ainda não foram analisados
        videos_to_analyze = []
        for video in videos_data:
            video_id = video.get('id_video')
            if video_id and video_id not in analyzed_video_ids:
                videos_to_analyze.append(video)
        
        print(f"🎯 Vídeos para analisar: {len(videos_to_analyze)}")
        
        if len(videos_to_analyze) == 0:
            print("✅ Todos os vídeos já foram analisados!")
            return existing_analyses, 0
        
        # Analisa vídeos concorrentemente
        new_analyses = await self.analyze_batch_videos(videos_to_analyze, prompt_template)
        
        # Combina com análises existentes
        all_analyses = existing_analyses + new_analyses
        
        # Salva resultados
        self.save_analyses(all_analyses, analyses_folder)
        
        print(f"\n📊 Resumo da análise:")
        print(f"   ✅ Vídeos analisados: {len(new_analyses)}")
        print(f"   📁 Total de análises: {len(all_analyses)}")
        
        return all_analyses, len(new_analyses)
    
    def load_existing_analyses(self, analyses_folder: str) -> List[Dict]:
        """Carrega análises existentes"""
        videos_analysis_file = os.path.join(analyses_folder, 'videos.json')
        
        if os.path.exists(videos_analysis_file):
            try:
                with open(videos_analysis_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ Erro ao carregar análises existentes: {e}")
                return []
        
        return []
    
    def save_analyses(self, analyses_data: List[Dict], analyses_folder: str):
        """Salva análises"""
        try:
            os.makedirs(analyses_folder, exist_ok=True)
            videos_analysis_file = os.path.join(analyses_folder, 'videos.json')
            
            with open(videos_analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analyses_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 Análises salvas: {len(analyses_data)} vídeos")
            return True
        except Exception as e:
            print(f"⚠️ Erro ao salvar análises: {e}")
            return False

# Função principal para usar a versão rápida
async def analisar_videos_rapido(videos_data: List[Dict], channel_folder: str):
    """Função principal para análise rápida de vídeos"""
    
    print("🚀 MODO RÁPIDO: Análise Concorrente de Vídeos")
    print("=" * 60)
    
    analisador = AnalisadorVideosRapido(
        max_concurrent_requests=10,  # 10 requisições simultâneas
        requests_per_minute=500      # Limite conservador
    )
    
    start_time = time.time()
    
    all_analyses, new_analyses = await analisador.process_all_videos(
        videos_data, channel_folder
    )
    
    elapsed = time.time() - start_time
    
    print(f"\n🎉 ANÁLISE CONCLUÍDA!")
    print(f"   ⏱️ Tempo total: {elapsed:.1f}s")
    print(f"   📊 Novos vídeos analisados: {new_analyses}")
    print(f"   📁 Total de análises: {len(all_analyses)}")
    
    if new_analyses > 0:
        print(f"   🚀 Velocidade: {new_analyses/elapsed:.1f} vídeos/segundo")
    
    return all_analyses, new_analyses

class Etapa2AnaliseVideos:
    """Classe para análise de vídeos com IA (versão rápida/concorrente)"""
    
    def __init__(self):
        pass
    
    def verificar_videos_disponiveis(self, channel_folder):
        """Verifica se há vídeos disponíveis para análise"""
        videos_file = os.path.join(channel_folder, '2_todosvideosdocanal.json')
        
        if not os.path.exists(videos_file):
            return False, []
        
        try:
            with open(videos_file, 'r', encoding='utf-8') as f:
                videos_data = json.load(f)
            return True, videos_data
        except Exception as e:
            print(f"⚠️ Erro ao carregar lista de vídeos: {e}")
            return False, []
    
    def executar(self, channel_folder):
        """Executa a análise de vídeos com IA (versão rápida)"""
        print("🎯 ETAPA 2: Análise de Vídeos com IA (Modo Rápido)")
        print("=" * 60)
        
        # Verifica se há vídeos disponíveis
        tem_videos, videos_data = self.verificar_videos_disponiveis(channel_folder)
        
        if not tem_videos:
            return "❌ Nenhum vídeo encontrado. Execute a Etapa 1 primeiro."
        
        if not videos_data:
            return "⚠️ Lista de vídeos está vazia."
        
        # Executa análise concorrente
        try:
            all_analyses, new_analyses = asyncio.run(
                analisar_videos_rapido(videos_data, channel_folder)
            )
            
            if len(all_analyses) > 0:
                return f"✅ Análise concluída! {new_analyses} novos vídeos analisados. Total: {len(all_analyses)} análises."
            else:
                return "❌ Nenhuma análise foi realizada. Verifique a configuração da API OpenAI."
                
        except Exception as e:
            return f"❌ Erro durante a análise: {e}"

if __name__ == "__main__":
    # Teste da etapa 2
    etapa2 = Etapa2AnaliseVideos()
    
    # Exemplo de uso
    channel_folder = "Mayra Gaiato _ Desenvolvimento Infantil e Autismo"
    if os.path.exists(channel_folder):
        result = etapa2.executar(channel_folder)
        print(f"\n🎉 Resultado: {result}")
    else:
        print("❌ Pasta do canal não encontrada. Execute as etapas anteriores primeiro.")
