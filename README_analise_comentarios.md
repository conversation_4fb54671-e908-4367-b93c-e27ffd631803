# 🚀 Análise de Comentários - Versão Otimizada

## 📋 Problemas Resolvidos

A versão otimizada do `etapa4_analise_comentarios.py` resolve os seguintes problemas que causavam travamento com vídeos que têm muitos comentários:

### ❌ Problemas Anteriores:
- **Processamento simultâneo excessivo**: Tentava processar todos os 800 comentários ao mesmo tempo
- **Sobrecarga de memória**: Criava 800 tasks simultâneas
- **Rate limiting inadequado**: Muitas requisições simultâneas para a API
- **Falta de recuperação**: Se travasse, tinha que recomeçar do zero
- **Sem monitoramento**: Não havia como acompanhar o progresso

### ✅ Melhorias Implementadas:
- **Processamento em lotes**: Processa comentários em grupos de 50
- **Concorrência reduzida**: Máximo 2 requisições simultâneas
- **Timeout por lote**: 5 minutos máximo por lote de 50 comentários
- **Salvamento incremental**: Salva progresso após cada vídeo
- **Backup automático**: Cria backup antes de salvar
- **Recuperação de erros**: Continua mesmo se um lote falhar
- **Monitoramento em tempo real**: Scripts para acompanhar progresso

## 🛠️ Como Usar

### 1. Executar Análise Normal
```bash
python etapa4_analise_comentarios.py
```

### 2. Monitorar Progresso (em outro terminal)
```bash
# Monitoramento em tempo real
python monitor_progresso.py "Nome da Pasta do Canal"

# Status detalhado por vídeo
python monitor_progresso.py "Nome da Pasta do Canal" status
```

### 3. Verificar Status da Análise
```bash
# Verifica quantos comentários faltam
python retomar_analise.py "Nome da Pasta do Canal" status
```

### 4. Retomar Análise Interrompida
```bash
# Continua de onde parou
python retomar_analise.py "Nome da Pasta do Canal" retomar
```

### 5. Limpar Análises Corrompidas
```bash
# Remove análises inválidas
python retomar_analise.py "Nome da Pasta do Canal" limpar
```

## 📊 Configurações Otimizadas

### Parâmetros Ajustados:
- **Lote**: 50 comentários por vez (era ilimitado)
- **Concorrência**: 2 requisições simultâneas (era 3)
- **Rate limit**: 100 req/min (era 150)
- **Timeout**: 30s por requisição + 5min por lote
- **Pausa entre lotes**: 2 segundos

### Para Vídeos com Muitos Comentários:
- **800 comentários** = 16 lotes de 50
- **Tempo estimado**: ~20-30 minutos
- **Salvamento**: A cada vídeo processado
- **Recuperação**: Automática se interrompido

## 🔧 Solução de Problemas

### Se a Análise Travar:
1. **Pare o processo** (Ctrl+C)
2. **Verifique o status**: `python retomar_analise.py "pasta" status`
3. **Retome a análise**: `python retomar_analise.py "pasta" retomar`

### Se Houver Erros de API:
- O script automaticamente tenta novamente
- Reduz velocidade se hit rate limit
- Pula comentários problemáticos
- Continua com o próximo lote

### Se Houver Análises Corrompidas:
```bash
python retomar_analise.py "pasta" limpar
```

## 📈 Monitoramento

### Exemplo de Saída do Monitor:
```
[14:30:15] 📊 450/800 (56.3%) | 🚀 2.1/s | ⏱️ ETA: 2.8min | 🆕 +12
```

### Legenda:
- **450/800**: 450 comentários processados de 800 total
- **(56.3%)**: Porcentagem concluída
- **🚀 2.1/s**: Velocidade atual (comentários por segundo)
- **⏱️ ETA: 2.8min**: Tempo estimado para conclusão
- **🆕 +12**: Novos comentários processados desde última verificação

## 🎯 Recomendações

### Para Canais Grandes (>500 comentários por vídeo):
1. **Use o monitor**: Sempre rode o monitor em paralelo
2. **Deixe rodar**: Não interrompa desnecessariamente
3. **Verifique logs**: Acompanhe mensagens de erro
4. **Tenha paciência**: Vídeos grandes levam tempo

### Para Melhor Performance:
- Execute durante horários de menor uso da API
- Certifique-se de ter boa conexão com internet
- Feche outros programas que usam muita CPU/memória

## 🚨 Avisos Importantes

- ⚠️ **Não execute múltiplas instâncias** do script simultaneamente
- ⚠️ **Não edite** arquivos de análise manualmente durante execução
- ⚠️ **Mantenha backup** da pasta do canal antes de grandes processamentos
- ⚠️ **Monitore uso da API** para não exceder limites do OpenAI

## 📞 Suporte

Se ainda houver problemas:
1. Verifique se a API key do OpenAI está configurada
2. Confirme que há créditos suficientes na conta OpenAI
3. Teste com um canal menor primeiro
4. Verifique logs de erro detalhados
