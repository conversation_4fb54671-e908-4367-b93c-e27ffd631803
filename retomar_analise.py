#!/usr/bin/env python3
"""
Script para retomar análise de comentários interrompida
Permite continuar de onde parou sem reprocessar comentários já analisados
"""

import json
import os
import sys
from etapa4_analise_comentarios import Etapa4AnaliseComentarios

def verificar_status_analise(channel_folder):
    """Verifica o status atual da análise"""
    
    analyses_file = os.path.join(channel_folder, 'analises', 'comentarios.json')
    videos_file = os.path.join(channel_folder, 'analises', 'videos.json')
    
    print("🔍 VERIFICANDO STATUS DA ANÁLISE")
    print("=" * 50)
    
    # Carrega vídeos relevantes
    if not os.path.exists(videos_file):
        print("❌ Arquivo de vídeos não encontrado. Execute a Etapa 2 primeiro.")
        return False
    
    try:
        with open(videos_file, 'r', encoding='utf-8') as f:
            all_videos = json.load(f)
        
        relevant_videos = [v for v in all_videos if v.get('relevante', False)]
        print(f"📹 Vídeos relevantes encontrados: {len(relevant_videos)}")
        
    except Exception as e:
        print(f"❌ Erro ao carregar vídeos: {e}")
        return False
    
    # Carrega análises existentes
    analyzed_comments = set()
    if os.path.exists(analyses_file):
        try:
            with open(analyses_file, 'r', encoding='utf-8') as f:
                analyses = json.load(f)
            
            for analysis in analyses:
                comment_id = analysis.get('id_comentario')
                if comment_id:
                    analyzed_comments.add(comment_id)
            
            print(f"✅ Comentários já analisados: {len(analyzed_comments)}")
            
        except Exception as e:
            print(f"⚠️ Erro ao carregar análises existentes: {e}")
    else:
        print("📝 Nenhuma análise anterior encontrada")
    
    # Conta comentários pendentes
    total_comments = 0
    pending_comments = 0
    videos_with_pending = []
    
    for video in relevant_videos:
        video_id = video.get('id_video')
        video_title = video.get('titulo_video', 'Sem título')
        
        comments_file = os.path.join(channel_folder, 'comentarios', f"{video_id}_comentarios.json")
        
        if os.path.exists(comments_file):
            try:
                with open(comments_file, 'r', encoding='utf-8') as f:
                    comments_data = json.load(f)
                
                video_comments = comments_data.get('comentarios', [])
                total_comments += len(video_comments)
                
                # Conta comentários pendentes neste vídeo
                video_pending = 0
                for comment in video_comments:
                    comment_id = comment.get('id_comentario')
                    if comment_id and comment_id not in analyzed_comments:
                        video_pending += 1
                
                if video_pending > 0:
                    videos_with_pending.append({
                        'video_id': video_id,
                        'titulo': video_title[:50],
                        'pendentes': video_pending,
                        'total': len(video_comments)
                    })
                
                pending_comments += video_pending
                
            except Exception as e:
                print(f"⚠️ Erro ao carregar comentários do vídeo {video_id}: {e}")
    
    print(f"💬 Total de comentários: {total_comments}")
    print(f"⏳ Comentários pendentes: {pending_comments}")
    print(f"📊 Progresso: {((total_comments - pending_comments) / total_comments * 100):.1f}%" if total_comments > 0 else "0%")
    
    if videos_with_pending:
        print(f"\n📋 Vídeos com comentários pendentes ({len(videos_with_pending)}):")
        for video in videos_with_pending[:10]:  # Mostra apenas os primeiros 10
            print(f"   • {video['titulo']}... ({video['pendentes']}/{video['total']})")
        
        if len(videos_with_pending) > 10:
            print(f"   ... e mais {len(videos_with_pending) - 10} vídeos")
    
    return pending_comments > 0

def limpar_analises_corrompidas(channel_folder):
    """Remove análises corrompidas ou incompletas"""
    
    analyses_file = os.path.join(channel_folder, 'analises', 'comentarios.json')
    
    if not os.path.exists(analyses_file):
        print("📝 Nenhum arquivo de análises encontrado")
        return
    
    try:
        with open(analyses_file, 'r', encoding='utf-8') as f:
            analyses = json.load(f)
        
        print(f"🔍 Verificando {len(analyses)} análises...")
        
        # Filtra análises válidas
        valid_analyses = []
        removed_count = 0
        
        for analysis in analyses:
            # Verifica se tem campos obrigatórios
            if (analysis.get('id_comentario') and 
                analysis.get('id_video') and 
                analysis.get('comentario') and
                'comentario_util' in analysis):
                valid_analyses.append(analysis)
            else:
                removed_count += 1
        
        if removed_count > 0:
            print(f"🧹 Removendo {removed_count} análises corrompidas...")
            
            # Salva backup
            backup_file = analyses_file.replace('.json', '_backup_limpeza.json')
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(analyses, f, ensure_ascii=False, indent=2)
            print(f"💾 Backup salvo em: {backup_file}")
            
            # Salva versão limpa
            with open(analyses_file, 'w', encoding='utf-8') as f:
                json.dump(valid_analyses, f, ensure_ascii=False, indent=2)
            
            print(f"✅ {len(valid_analyses)} análises válidas mantidas")
        else:
            print("✅ Todas as análises estão válidas")
            
    except Exception as e:
        print(f"❌ Erro ao limpar análises: {e}")

def main():
    if len(sys.argv) < 2:
        print("Uso: python retomar_analise.py <pasta_do_canal> [acao]")
        print("Ações:")
        print("  status   - Verifica status da análise (padrão)")
        print("  retomar  - Retoma análise interrompida")
        print("  limpar   - Remove análises corrompidas")
        sys.exit(1)
    
    channel_folder = sys.argv[1]
    action = sys.argv[2] if len(sys.argv) > 2 else "status"
    
    if not os.path.exists(channel_folder):
        print(f"❌ Pasta não encontrada: {channel_folder}")
        sys.exit(1)
    
    if action == "limpar":
        limpar_analises_corrompidas(channel_folder)
    elif action == "retomar":
        print("🔄 RETOMANDO ANÁLISE DE COMENTÁRIOS")
        print("=" * 50)
        
        # Verifica se há trabalho pendente
        has_pending = verificar_status_analise(channel_folder)
        
        if not has_pending:
            print("✅ Não há comentários pendentes para analisar!")
            sys.exit(0)
        
        print("\n🚀 Iniciando análise...")
        
        # Executa análise
        etapa4 = Etapa4AnaliseComentarios()
        result = etapa4.executar(channel_folder)
        print(f"\n🎉 Resultado: {result}")
        
    else:  # status
        verificar_status_analise(channel_folder)

if __name__ == "__main__":
    main()
