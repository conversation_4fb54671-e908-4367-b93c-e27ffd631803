#!/usr/bin/env python3
"""
Script para separar comentários úteis por categoria resumida

Este script:
1. Lê o arquivo comentarios.json
2. Filtra comentários onde "comentario_util": true
3. Agrupa comentários por campo "categoria_resumida"
4. Salva cada categoria em um arquivo separado: relatorios/categorias/{categoria}_comentarios.txt
"""

import json
import os
import re
from datetime import datetime

def sanitize_filename(filename):
    """Remove caracteres inválidos do nome do arquivo"""
    # Remove/substitui caracteres especiais
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
    # Remove espaços extras e substitui por underscore
    filename = re.sub(r'\s+', '_', filename)
    # Remove caracteres especiais adicionais
    filename = re.sub(r'[^\w\-_.]', '', filename)
    # Converte para minúsculo
    filename = filename.lower().strip('_')
    return filename

def separate_comments_by_category(channel_folder):
    """Separa comentários úteis por categoria resumida"""
    
    # Arquivo de entrada
    comments_file = os.path.join(channel_folder, 'analises', 'comentarios.json')
    
    if not os.path.exists(comments_file):
        print("❌ Arquivo de comentários não encontrado:")
        print(f"   📁 {comments_file}")
        print("💡 Execute a Etapa 4 (Análise de Comentários) primeiro!")
        return False
    
    print(f"📂 Carregando comentários de: {comments_file}")
    
    try:
        with open(comments_file, 'r', encoding='utf-8') as f:
            comments_data = json.load(f)
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo de comentários: {e}")
        return False
    
    print(f"📊 Total de comentários carregados: {len(comments_data)}")
    
    # Filtra comentários úteis
    useful_comments = [comment for comment in comments_data if comment.get('comentario_util', False)]
    print(f"✅ Comentários úteis encontrados: {len(useful_comments)}")
    
    if len(useful_comments) == 0:
        print("⚠️ Nenhum comentário útil encontrado!")
        return False
    
    # Agrupa comentários por categoria resumida
    comments_by_category = {}
    comments_without_category = []
    
    for comment in useful_comments:
        category = comment.get('categoria_resumida', '').strip()
        
        if category and category.lower() not in ['', 'não definido', 'indefinido', 'unknown']:
            # Normaliza o nome da categoria
            category_normalized = category.title()  # Primeira letra maiúscula
            
            if category_normalized not in comments_by_category:
                comments_by_category[category_normalized] = []
            
            comments_by_category[category_normalized].append(comment)
        else:
            comments_without_category.append(comment)
    
    print(f"🏷️ Categorias encontradas: {len(comments_by_category)}")
    print(f"❓ Comentários sem categoria: {len(comments_without_category)}")
    
    # Mostra estatísticas por categoria
    print(f"\n📊 DISTRIBUIÇÃO POR CATEGORIA:")
    for category, comments in sorted(comments_by_category.items(), key=lambda x: len(x[1]), reverse=True):
        print(f"   📌 {category}: {len(comments)} comentários")
    
    if comments_without_category:
        print(f"   ❓ Sem categoria: {len(comments_without_category)} comentários")
    
    # Cria pasta de categorias
    categories_folder = os.path.join(channel_folder, 'relatorios', 'categorias')
    os.makedirs(categories_folder, exist_ok=True)
    
    def format_number(num):
        """Formata números de curtidas"""
        if num >= 1_000:
            return f"{num/1_000:.1f}K"
        else:
            return str(num)
    
    # Salva cada categoria em arquivo separado (formato TXT)
    files_created = 0
    timestamp = datetime.now()
    
    for category, comments in comments_by_category.items():
        # Nome do arquivo sanitizado
        category_filename = sanitize_filename(category)
        output_file = os.path.join(categories_folder, f"{category_filename}_comentarios.txt")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                # Cabeçalho
                f.write("=" * 80 + "\n")
                f.write(f"RELATÓRIO DE COMENTÁRIOS - CATEGORIA: {category.upper()}\n")
                f.write("=" * 80 + "\n")
                f.write(f"Gerado em: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Fonte: {comments_file}\n")
                f.write(f"Total de comentários: {len(comments)}\n")
                f.write("=" * 80 + "\n\n")
                
                # Lista comentários
                for i, comment in enumerate(comments, 1):
                    video_id = comment.get('id_video', 'N/A')
                    comment_id = comment.get('id_comentario', 'N/A')
                    comment_text = comment.get('comentario', 'Texto não disponível')
                    resumo = comment.get('resumo', 'Resumo não disponível')
                    curtidas = comment.get('curtidas_comentario', 0)
                    curtidas_formatted = format_number(curtidas) if curtidas else "0"
                    
                    f.write(f"{i:3d}. ({video_id}) ({comment_id}) ({curtidas_formatted} curtidas)\n")
                    f.write(f"- Comentário: {comment_text}\n")
                    f.write(f"- Resumo IA: {resumo}\n\n")
            
            print(f"   💾 {category}: {len(comments)} comentários → {output_file}")
            files_created += 1
            
        except Exception as e:
            print(f"   ❌ Erro ao salvar {category}: {e}")
    
    # Salva comentários sem categoria se existirem
    if comments_without_category:
        output_file = os.path.join(categories_folder, "sem_categoria_comentarios.txt")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                # Cabeçalho
                f.write("=" * 80 + "\n")
                f.write("RELATÓRIO DE COMENTÁRIOS - CATEGORIA: SEM CATEGORIA DEFINIDA\n")
                f.write("=" * 80 + "\n")
                f.write(f"Gerado em: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Fonte: {comments_file}\n")
                f.write(f"Total de comentários: {len(comments_without_category)}\n")
                f.write("=" * 80 + "\n\n")
                
                # Lista comentários
                for i, comment in enumerate(comments_without_category, 1):
                    video_id = comment.get('id_video', 'N/A')
                    comment_id = comment.get('id_comentario', 'N/A')
                    comment_text = comment.get('comentario', 'Texto não disponível')
                    resumo = comment.get('resumo', 'Resumo não disponível')
                    curtidas = comment.get('curtidas_comentario', 0)
                    curtidas_formatted = format_number(curtidas) if curtidas else "0"
                    
                    f.write(f"{i:3d}. ({video_id}) ({comment_id}) ({curtidas_formatted} curtidas)\n")
                    f.write(f"- Comentário: {comment_text}\n")
                    f.write(f"- Resumo IA: {resumo}\n\n")
            
            print(f"   💾 Sem categoria: {len(comments_without_category)} comentários → {output_file}")
            files_created += 1
            
        except Exception as e:
            print(f"   ❌ Erro ao salvar comentários sem categoria: {e}")
    
    # Cria arquivo de resumo
    summary_file = os.path.join(categories_folder, "_resumo_categorias.txt")
    
    try:
        with open(summary_file, 'w', encoding='utf-8') as f:
            # Cabeçalho
            f.write("=" * 80 + "\n")
            f.write("RESUMO GERAL - COMENTÁRIOS POR CATEGORIA\n")
            f.write("=" * 80 + "\n")
            f.write(f"Gerado em: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Fonte: {comments_file}\n")
            f.write(f"Total de comentários úteis: {len(useful_comments)}\n")
            f.write(f"Total de categorias: {len(comments_by_category)}\n")
            f.write(f"Comentários sem categoria: {len(comments_without_category)}\n")
            f.write("=" * 80 + "\n\n")
            
            # Distribuição por categoria (ordenado por quantidade)
            f.write("DISTRIBUIÇÃO POR CATEGORIA (ordenado por quantidade):\n")
            f.write("-" * 50 + "\n\n")
            
            sorted_categories = sorted(comments_by_category.items(), key=lambda x: len(x[1]), reverse=True)
            for i, (category, comments) in enumerate(sorted_categories, 1):
                f.write(f"{i:2d}. {category}: {len(comments)} comentários\n")
            
            if comments_without_category:
                f.write(f"\n    Sem categoria: {len(comments_without_category)} comentários\n")
        
        print(f"   📋 Resumo salvo: {summary_file}")
        files_created += 1
        
    except Exception as e:
        print(f"   ❌ Erro ao salvar resumo: {e}")
    
    print(f"\n📁 Pasta de categorias: {categories_folder}")
    print(f"📄 Arquivos criados: {files_created}")
    
    return True

def main():
    """Função principal"""
    print("🎯 SEPARAÇÃO DE COMENTÁRIOS POR CATEGORIA")
    print("=" * 60)
    
    # Procura pela pasta do canal
    channel_folder = "Mayra Gaiato _ Desenvolvimento Infantil e Autismo"
    
    if not os.path.exists(channel_folder):
        print(f"❌ Pasta do canal não encontrada: {channel_folder}")
        print("💡 Execute as etapas anteriores primeiro")
        return
    
    print(f"📁 Pasta do canal: {channel_folder}")
    
    # Separa comentários por categoria
    success = separate_comments_by_category(channel_folder)
    
    if success:
        print("\n🎉 SEPARAÇÃO CONCLUÍDA COM SUCESSO!")
        print("📋 Verifique os arquivos gerados na pasta 'relatorios/categorias'")
        print("💡 Cada categoria foi salva em um arquivo TXT separado")
    else:
        print("\n❌ FALHA NA SEPARAÇÃO!")
        print("💡 Verifique se a Etapa 4 foi executada corretamente")

if __name__ == "__main__":
    main()
