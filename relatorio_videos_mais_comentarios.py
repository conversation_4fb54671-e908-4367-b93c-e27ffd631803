#!/usr/bin/env python3
"""
Script para gerar relatório dos vídeos com mais comentários do canal

Este script:
1. Lê o arquivo 2_todosvideosdocanal.json
2. Ordena vídeos por número de comentários (maior para menor)
3. Salva relatório em relatorios/videos_mais_comentarios.txt
4. Inclui estatísticas e análises dos dados
"""

import json
import os
from datetime import datetime

def format_number(num):
    """Formata números grandes de forma legível"""
    if num >= 1_000_000:
        return f"{num/1_000_000:.1f}M"
    elif num >= 1_000:
        return f"{num/1_000:.1f}K"
    else:
        return str(num)

def analyze_videos_by_comments(channel_folder):
    """Analisa vídeos por comentários"""
    
    # Arquivo de entrada
    videos_file = os.path.join(channel_folder, '2_todosvideosdocanal.json')
    
    if not os.path.exists(videos_file):
        print("❌ Arquivo de vídeos não encontrado:")
        print(f"   📁 {videos_file}")
        print("💡 Execute a Etapa 1 (Extração do Canal) primeiro!")
        return False
    
    print(f"📂 Carregando vídeos de: {videos_file}")
    
    try:
        with open(videos_file, 'r', encoding='utf-8') as f:
            videos_data = json.load(f)
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo de vídeos: {e}")
        return False
    
    print(f"📊 Total de vídeos carregados: {len(videos_data)}")
    
    if len(videos_data) == 0:
        print("⚠️ Nenhum vídeo encontrado!")
        return False
    
    # Filtra vídeos que têm dados de comentários
    videos_with_comments = []
    videos_without_comments = 0
    
    for video in videos_data:
        comments = video.get('numero_comentarios')
        if comments is not None and isinstance(comments, (int, float)) and comments >= 0:
            videos_with_comments.append(video)
        else:
            videos_without_comments += 1
    
    print(f"✅ Vídeos com dados de comentários: {len(videos_with_comments)}")
    print(f"⚠️ Vídeos sem dados de comentários: {videos_without_comments}")
    
    if len(videos_with_comments) == 0:
        print("❌ Nenhum vídeo com dados de comentários encontrado!")
        return False
    
    # Ordena por comentários (maior para menor)
    videos_sorted = sorted(videos_with_comments, key=lambda x: x.get('numero_comentarios', 0), reverse=True)
    
    # Calcula estatísticas
    total_comments = sum(video.get('numero_comentarios', 0) for video in videos_sorted)
    avg_comments = total_comments / len(videos_sorted)
    median_index = len(videos_sorted) // 2
    median_comments = videos_sorted[median_index].get('numero_comentarios', 0)
    
    top_10_comments = sum(video.get('numero_comentarios', 0) for video in videos_sorted[:10])
    top_10_percentage = (top_10_comments / total_comments) * 100 if total_comments > 0 else 0
    
    print(f"\n📊 ESTATÍSTICAS DE COMENTÁRIOS:")
    print(f"   💬 Total de comentários: {format_number(total_comments)} ({total_comments:,})")
    print(f"   📊 Média de comentários: {format_number(avg_comments)} ({avg_comments:,.0f})")
    print(f"   📊 Mediana de comentários: {format_number(median_comments)} ({median_comments:,})")
    print(f"   🏆 Top 10 representam: {top_10_percentage:.1f}% dos comentários")
    
    # Mostra top 10
    print(f"\n🏆 TOP 10 VÍDEOS COM MAIS COMENTÁRIOS:")
    for i, video in enumerate(videos_sorted[:10], 1):
        title = video.get('titulo', 'Título não disponível')[:60]
        comments = video.get('numero_comentarios', 0)
        print(f"   {i:2d}. {format_number(comments):>8s} - {title}...")
    
    # Cria pasta de relatórios
    reports_folder = os.path.join(channel_folder, 'relatorios')
    os.makedirs(reports_folder, exist_ok=True)
    
    # Arquivos de saída
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = os.path.join(reports_folder, f'videos_mais_comentarios_{timestamp}.txt')
    main_output_file = os.path.join(reports_folder, 'videos_mais_comentarios.txt')
    
    def save_report(file_path):
        """Salva o relatório em formato TXT"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                # Cabeçalho
                f.write("=" * 80 + "\n")
                f.write("RELATÓRIO DE VÍDEOS COM MAIS COMENTÁRIOS\n")
                f.write("=" * 80 + "\n")
                f.write(f"Gerado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Fonte: {videos_file}\n")
                f.write(f"Total de vídeos: {len(videos_data)}\n")
                f.write(f"Vídeos com comentários: {len(videos_with_comments)}\n")
                f.write(f"Vídeos sem comentários: {videos_without_comments}\n")
                f.write(f"Total de comentários: {format_number(total_comments)} ({total_comments:,})\n")
                f.write(f"Média de comentários: {format_number(avg_comments)} ({avg_comments:,.0f})\n")
                f.write(f"Mediana de comentários: {format_number(median_comments)} ({median_comments:,})\n")
                f.write(f"Top 10 representam: {top_10_percentage:.1f}% dos comentários\n")
                f.write("=" * 80 + "\n\n")
                
                # Lista de vídeos (uma por linha)
                for i, video in enumerate(videos_sorted, 1):
                    title = video.get('titulo', 'Título não disponível')
                    comments = video.get('numero_comentarios', 0)
                    comments_formatted = format_number(comments)
                    data_upload = video.get('data_upload', 'N/A')
                    
                    f.write(f"{i:3d}. ({comments_formatted}) ({data_upload}) - {title}\n")
            
            return True
        except Exception as e:
            print(f"❌ Erro ao salvar {file_path}: {e}")
            return False
    
    # Salva arquivos
    success_count = 0
    
    if save_report(output_file):
        print(f"💾 Relatório com timestamp salvo: {output_file}")
        success_count += 1
    
    if save_report(main_output_file):
        print(f"💾 Relatório principal atualizado: {main_output_file}")
        success_count += 1
    
    return success_count > 0

def main():
    """Função principal"""
    print("🎯 RELATÓRIO DE VÍDEOS COM MAIS COMENTÁRIOS")
    print("=" * 60)
    
    # Procura pela pasta do canal
    channel_folder = "Mayra Gaiato _ Desenvolvimento Infantil e Autismo"
    
    if not os.path.exists(channel_folder):
        print(f"❌ Pasta do canal não encontrada: {channel_folder}")
        print("💡 Execute as etapas anteriores primeiro")
        return
    
    print(f"📁 Pasta do canal: {channel_folder}")
    
    # Gera relatório de comentários
    success = analyze_videos_by_comments(channel_folder)
    
    if success:
        print("\n🎉 RELATÓRIO GERADO COM SUCESSO!")
        print("📋 Verifique o arquivo gerado na pasta 'relatorios'")
        print("💬 O ranking completo está ordenado do mais para o menos comentado")
    else:
        print("\n❌ FALHA NA GERAÇÃO DO RELATÓRIO!")
        print("💡 Verifique se a Etapa 1 foi executada corretamente")

if __name__ == "__main__":
    main()
