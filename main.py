from etapa1_extracao_canal import Etapa1ExtracaoCanal
from etapa2_analise_videos import Etapa2AnaliseVideos
from etapa3_extracao_comentarios import Etapa3ExtracaoComentarios
from etapa4_analise_comentarios import Etapa4AnaliseComentarios
from etapa5_extracao_transcricoes import Etapa5ExtracaoTranscricoes
from etapa6_conversao_transcricao_texto import Etapa6ConversaoTranscricaoTexto

class PersonaCreatorOrchestrator:
    def __init__(self):
        self.etapa1 = Etapa1ExtracaoCanal()
        self.etapa2 = Etapa2AnaliseVideos()
        self.etapa3 = Etapa3ExtracaoComentarios()
        self.etapa4 = Etapa4AnaliseComentarios()
        self.etapa5 = Etapa5ExtracaoTranscricoes()
        self.etapa6 = Etapa6ConversaoTranscricaoTexto()
        # Aqui adicionaremos as outras etapas conforme forem criadas
        # self.etapa7 = Etapa7AnaliseConteudo()
        # self.etapa8 = Etapa8CriacaoPersona()

    def start_process(self):
        """Inicia o processo conversacional completo"""
        print("🎯 Bem-vindo ao Criador de Personas baseado em YouTube!")
        print("=" * 60)
        print("📋 Este processo tem várias etapas:")
        print("   1️⃣ Extração de dados do canal")
        print("   2️⃣ Análise de conteúdo dos vídeos")
        print("   3️⃣ Criação da persona")
        print("   4️⃣ Relatório final")
        print("=" * 60)

        # Solicita URL do canal
        channel_url = input("\n📺 Por favor, insira a URL do canal do YouTube: ").strip()

        if not channel_url:
            print("❌ URL não fornecida. Encerrando...")
            return

        # Pergunta quantos vídeos extrair
        try:
            resposta = input("🔢 Quantos vídeos extrair? (Enter = todos, ou digite um número): ").strip()
            if resposta == "":
                max_videos = None  # None significa todos os vídeos
                print("📊 Configurado para extrair TODOS os vídeos do canal")
            else:
                max_videos = int(resposta)
                print(f"📊 Configurado para extrair {max_videos} vídeos")
        except ValueError:
            max_videos = 50
            print(f"📊 Valor inválido, usando padrão: {max_videos} vídeos")

        try:
            # ETAPA 1: Extração de dados
            print("\n" + "🔄" * 20)
            result_etapa1 = self.etapa1.executar(channel_url, max_videos)

            # Extrai o caminho da pasta criada do resultado da etapa 1
            if "Pasta criada:" in result_etapa1 or "Pasta existente:" in result_etapa1:
                lines = result_etapa1.split('\n')
                folder_path = None
                for line in lines:
                    if "Pasta criada:" in line or "Pasta existente:" in line:
                        folder_path = line.split(': ')[1].strip()
                        break

                if folder_path:
                    # ETAPA 2: Análise de vídeos com IA
                    print("\n" + "🔄" * 20)
                    print("🤖 Configurado para analisar vídeos com IA e filtrar relevância")

                    result_etapa2 = self.etapa2.executar(folder_path)

                    # ETAPA 3: Extração de comentários (só dos relevantes)
                    print("\n" + "🔄" * 20)
                    print("💬 Configurado para extrair comentários APENAS dos vídeos relevantes")

                    result_etapa3 = self.etapa3.executar(folder_path)

                    # ETAPA 4: Análise de comentários com IA
                    print("\n" + "🔄" * 20)
                    print("💬 Configurado para analisar comentários dos vídeos relevantes com IA")

                    result_etapa4 = self.etapa4.executar(folder_path)

                    # ETAPA 5: Extração de transcrições (só dos relevantes)
                    print("\n" + "🔄" * 20)
                    print("📝 Configurado para extrair transcrições APENAS dos vídeos relevantes")

                    result_etapa5 = self.etapa5.executar(folder_path)

                    # ETAPA 6: Conversão de transcrições para texto
                    print("\n" + "🔄" * 20)
                    print("📄 Configurado para converter transcrições JSON em arquivos TXT")

                    result_etapa6 = self.etapa6.executar(folder_path)

            print("\n" + "🔄" * 20)
            print("🎉 PROCESSO CONCLUÍDO!")
            print("📁 Verifique a pasta criada com os arquivos gerados.")

        except Exception as e:
            print(f"\n❌ Erro durante o processo: {str(e)}")
            print("🔄 Tente novamente ou verifique a URL do canal.")

if __name__ == "__main__":
    orchestrator = PersonaCreatorOrchestrator()
    orchestrator.start_process()
