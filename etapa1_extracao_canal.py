import os
import json
import re
import yt_dlp
import sys
import time
import random
from datetime import datetime

def sanitize_filename(filename):
    """Remove caracteres inválidos do nome do arquivo/pasta"""
    return re.sub(r'[<>:"/\\|?*]', '_', filename).strip()

def format_channel_data_pt(info, channel_url):
    """Formata dados essenciais do canal em português"""
    return {
        'extraido_em': datetime.now().isoformat(),
        'id_canal': info.get('channel_id'),
        'nome_canal': info.get('title', '').replace(' - Videos', '').replace(' - Vídeos', ''),
        'descricao': info.get('description', ''),
        'numero_seguidores': info.get('channel_follower_count'),
        'total_visualizacoes': info.get('view_count'),
        'url_canal': channel_url,
        'nome_criador': info.get('uploader'),
        'id_criador': info.get('uploader_id'),
        'url_criador': info.get('channel_url'),
        'total_videos_encontrados': len(info.get('entries', [])) if info.get('entries') else 0
    }

def format_video_data_pt(entry):
    """Formata dados essenciais do vídeo em português"""

    # Formata data de upload se disponível
    upload_date = entry.get('upload_date')
    if upload_date and len(str(upload_date)) == 8:
        # Converte YYYYMMDD para YYYY-MM-DD
        upload_date_str = str(upload_date)
        formatted_date = f"{upload_date_str[:4]}-{upload_date_str[4:6]}-{upload_date_str[6:8]}"
    else:
        formatted_date = upload_date

    # Formata duração em formato legível
    duration_seconds = entry.get('duration')
    duration_formatted = None
    if duration_seconds:
        minutes = int(duration_seconds // 60)
        seconds = int(duration_seconds % 60)
        duration_formatted = f"{minutes}:{seconds:02d}"

    return {
        'id_video': entry.get('id'),
        'titulo': entry.get('title', 'Título não disponível'),
        'url_video': f"https://www.youtube.com/watch?v={entry.get('id')}",
        'duracao_formatada': duration_formatted,
        'numero_visualizacoes': entry.get('view_count'),
        'numero_likes': entry.get('like_count'),
        'data_upload': formatted_date,
        'descricao': entry.get('description', ''),
        'miniatura': entry.get('thumbnail'),
        'categoria': entry.get('category'),
        'tags': entry.get('tags', []),
        'numero_comentarios': entry.get('comment_count')
    }

def extract_channel_and_videos(channel_url, max_videos=100):
    """Extrai informações do canal e vídeos com feedback visual otimizado"""

    print("🔍 Extraindo informações do canal e vídeos...")
    if max_videos is None:
        print("   📊 Extraindo TODOS os vídeos do canal (pode demorar alguns minutos)")
    else:
        print(f"   📊 Limitando a {max_videos} vídeos mais recentes")

    try:
        # ETAPA 1: Extrai lista básica dos vídeos (rápido)
        print(f"   📡 Conectando ao YouTube...")
        videos_url = f"{channel_url}/videos"
        print(f"   🔗 URL: {videos_url}")
        print(f"   ⏳ Obtendo lista de vídeos...")

        ydl_opts_basic = {
            'quiet': True,
            'extract_flat': True,  # Apenas lista básica
            'playlistend': max_videos if max_videos is not None else None,
        }

        with yt_dlp.YoutubeDL(ydl_opts_basic) as ydl:
            info = ydl.extract_info(videos_url, download=False)

        print(f"   ✅ Lista obtida com sucesso!")

        # Dados do canal em português
        channel_data = format_channel_data_pt(info, channel_url)

        print(f"   ✅ Canal: {channel_data['nome_canal']}")
        print(f"   👥 Seguidores: {channel_data.get('numero_seguidores', 'N/A')}")

        # ETAPA 2: Verifica quais vídeos já foram extraídos
        channel_name = sanitize_filename(channel_data['nome_canal'])
        folder_path = os.path.join(os.getcwd(), channel_name)
        os.makedirs(folder_path, exist_ok=True)

        videos_file = os.path.join(folder_path, '2_todosvideosdocanal.json')
        videos_data = []
        videos_ja_extraidos = set()

        if os.path.exists(videos_file):
            try:
                with open(videos_file, 'r', encoding='utf-8') as f:
                    videos_data = json.load(f)
                    videos_ja_extraidos = {video['id_video'] for video in videos_data}
                    print(f"   📊 Encontrados {len(videos_ja_extraidos)} vídeos já extraídos")
            except Exception as e:
                print(f"   ⚠️ Erro ao carregar vídeos existentes: {e}")

        # Salva dados do canal imediatamente
        print(f"   💾 Salvando dados do canal...")
        save_progress_incremental(channel_data, videos_data, folder_path)

        # ETAPA 3: Identifica vídeos que precisam ser extraídos
        if 'entries' in info and info['entries']:
            entries = [e for e in info['entries'] if e is not None]
            total = len(entries)
            print(f"   📋 Encontrados {total} vídeos na lista")

            # Filtra vídeos que ainda não foram extraídos
            videos_para_extrair = []
            for entry in entries:
                if entry and entry.get('id') and entry.get('id') not in videos_ja_extraidos:
                    videos_para_extrair.append(entry)

            print(f"   🎯 {len(videos_para_extrair)} vídeos novos para extrair")

            if len(videos_para_extrair) == 0:
                print(f"   ✅ Todos os vídeos já foram extraídos!")
                return channel_data, videos_data

            # ETAPA 4: Extrai metadados detalhados apenas dos vídeos novos
            print(f"   🔄 Iniciando extração detalhada dos vídeos novos...")

            # Configuração para extração detalhada com rate limiting
            ydl_opts_detailed = {
                'quiet': False,
                'no_warnings': True,
                'extract_flat': False,
                'writesubtitles': False,
                'writeautomaticsub': False,
                'geo_bypass': True,
                'sleep_requests': 2,
                'sleep_interval': 3,
                'max_sleep_interval': 6,
            }

            videos_longos = len(videos_data)
            shorts_filtrados = 0
            videos_processados = 0

            with yt_dlp.YoutubeDL(ydl_opts_detailed) as ydl:
                for entry in videos_para_extrair:
                    if not entry or not entry.get('id'):
                        continue

                    video_id = entry.get('id')
                    video_title = entry.get('title', 'Título não disponível')[:50]
                    video_url = f"https://www.youtube.com/watch?v={video_id}"

                    videos_processados += 1
                    print(f"   📹 Extraindo vídeo {videos_processados}/{len(videos_para_extrair)}: {video_title}...")
                    sys.stdout.flush()

                    try:
                        # Extrai metadados detalhados apenas deste vídeo
                        video_info = ydl.extract_info(video_url, download=False)

                        # Filtra vídeos longos (não shorts)
                        duration = video_info.get('duration')
                        if duration is not None and duration <= 60:
                            shorts_filtrados += 1
                            print(f"      ⏭️ Short filtrado (duração: {duration}s)")
                            continue  # Pula shorts

                        # Formata dados do vídeo em português
                        video_data = format_video_data_pt(video_info)
                        videos_data.append(video_data)
                        videos_longos += 1

                        # Mostra informações do vídeo extraído
                        duration_str = video_data.get('duracao_formatada', 'N/A')
                        views = video_data.get('numero_visualizacoes', 'N/A')
                        print(f"      ✅ Extraído: {duration_str} | {views} views")

                        # Salva progresso a cada 5 vídeos ou se for o último
                        if (videos_processados % 5 == 0) or (videos_processados == len(videos_para_extrair)):
                            print(f"      💾 Salvando progresso... ({len(videos_data)} vídeos total)")
                            save_progress_incremental(channel_data, videos_data, folder_path)

                    except Exception as e:
                        print(f"      ⚠️ Erro ao processar: {e}")
                        continue

            print(f"\n   ✅ Extração concluída!")
            print(f"   📊 Resumo final: {len(videos_data)} vídeos longos | {shorts_filtrados} shorts filtrados | {videos_processados} novos extraídos")
            
            return channel_data, videos_data
            
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        return None, []

def save_progress_incremental(channel_data, videos_data, folder_path):
    """Salva progresso incremental durante a extração"""
    try:
        # Salva dados do canal
        channel_file = os.path.join(folder_path, '1_informacoesdocanal.json')
        with open(channel_file, 'w', encoding='utf-8') as f:
            json.dump(channel_data, f, ensure_ascii=False, indent=2)

        # Salva dados dos vídeos
        videos_file = os.path.join(folder_path, '2_todosvideosdocanal.json')
        with open(videos_file, 'w', encoding='utf-8') as f:
            json.dump(videos_data, f, ensure_ascii=False, indent=2)

        # Mostra timestamp do salvamento
        timestamp = datetime.now().strftime("%H:%M:%S")
        print(f"         ✅ Salvo em {timestamp} - {len(videos_data)} vídeos")

        return True
    except Exception as e:
        print(f"⚠️ Erro ao salvar progresso: {e}")
        return False

def save_data(channel_data, videos_data):
    """Salva os dados finais em arquivos JSON"""

    # Cria pasta
    channel_name = sanitize_filename(channel_data['nome_canal'])
    folder_path = os.path.join(os.getcwd(), channel_name)

    print(f"📁 Criando pasta: {channel_name}")
    os.makedirs(folder_path, exist_ok=True)

    # Salva dados do canal
    print("💾 Salvando informações do canal...")
    channel_file = os.path.join(folder_path, '1_informacoesdocanal.json')
    with open(channel_file, 'w', encoding='utf-8') as f:
        json.dump(channel_data, f, ensure_ascii=False, indent=2)

    # Salva dados dos vídeos
    print("💾 Salvando informações dos vídeos...")
    videos_file = os.path.join(folder_path, '2_todosvideosdocanal.json')
    with open(videos_file, 'w', encoding='utf-8') as f:
        json.dump(videos_data, f, ensure_ascii=False, indent=2)

    return folder_path, channel_file, videos_file

def main():
    print("🎯 EXTRATOR DE DADOS DO YOUTUBE - VERSÃO FUNCIONAL")
    print("=" * 60)
    
    # Solicita URL do canal
    channel_url = input("📺 Digite a URL do canal do YouTube: ").strip()
    
    if not channel_url:
        print("❌ URL não fornecida.")
        return
    
    # Pergunta quantos vídeos extrair
    try:
        max_videos = int(input("🔢 Quantos vídeos extrair? (padrão: 100): ") or "100")
    except ValueError:
        max_videos = 100
    
    print(f"\n🚀 Iniciando extração para: {channel_url}")
    print("-" * 60)
    
    # Extrai dados
    channel_data, videos_data = extract_channel_and_videos(channel_url, max_videos)
    
    if not channel_data:
        print("❌ Falha na extração.")
        return
    
    if not videos_data:
        print("⚠️ Nenhum vídeo longo foi encontrado.")
        return
    
    # Salva dados
    folder_path, channel_file, videos_file = save_data(channel_data, videos_data)
    
    # Relatório final
    print("\n" + "=" * 60)
    print("✅ EXTRAÇÃO CONCLUÍDA COM SUCESSO!")
    print(f"📁 Pasta criada: {folder_path}")
    print(f"📄 Arquivo do canal: {os.path.basename(channel_file)}")
    print(f"📄 Arquivo de vídeos: {os.path.basename(videos_file)}")
    print(f"🎥 Total de vídeos extraídos: {len(videos_data)}")
    print("=" * 60)
    
    # Mostra alguns exemplos
    print("\n📋 Primeiros vídeos encontrados:")
    for i, video in enumerate(videos_data[:5]):
        duration_str = video.get('duracao_formatada', 'N/A')
        views = video.get('numero_visualizacoes', 'N/A')
        date = video.get('data_upload', 'N/A')
        print(f"   {i+1}. {video['titulo'][:50]}...")
        print(f"      ⏱️ {duration_str} | 👁️ {views} views | 📅 {date}")

class Etapa1ExtracaoCanal:
    """Classe para ser usada pelo main.py"""

    def __init__(self):
        pass

    def verificar_arquivos_existentes(self, channel_url: str):
        """Verifica se os arquivos da etapa 1 já existem"""
        try:
            # Primeiro, precisa extrair o nome do canal para verificar a pasta
            ydl_opts = {'quiet': True, 'extract_flat': True}
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(f"{channel_url}/videos", download=False)
                channel_name = sanitize_filename(info.get('title', '').replace(' - Videos', '').replace(' - Vídeos', ''))

            folder_path = os.path.join(os.getcwd(), channel_name)
            channel_file = os.path.join(folder_path, '1_informacoesdocanal.json')
            videos_file = os.path.join(folder_path, '2_todosvideosdocanal.json')

            if os.path.exists(channel_file) and os.path.exists(videos_file):
                return True, folder_path, channel_file, videos_file
            else:
                return False, folder_path, channel_file, videos_file

        except Exception as e:
            print(f"⚠️ Erro ao verificar arquivos existentes: {e}")
            return False, None, None, None

    def verificar_progresso_parcial(self, channel_url: str):
        """Verifica se há progresso parcial e quantos vídeos já foram extraídos"""
        try:
            arquivos_existem, folder_path, channel_file, videos_file = self.verificar_arquivos_existentes(channel_url)

            if arquivos_existem:
                with open(videos_file, 'r', encoding='utf-8') as f:
                    videos_data = json.load(f)
                return True, len(videos_data), videos_data
            else:
                return False, 0, []

        except Exception as e:
            print(f"⚠️ Erro ao verificar progresso parcial: {e}")
            return False, 0, []

    def executar(self, channel_url: str, max_videos = 100):
        """Executa a extração de dados do canal"""
        print("🎯 ETAPA 1: Extração de Dados do Canal")
        print("=" * 50)

        # Verifica se há progresso parcial
        tem_progresso, videos_ja_extraidos, videos_existentes = self.verificar_progresso_parcial(channel_url)

        if tem_progresso:
            if max_videos is None or videos_ja_extraidos >= max_videos:
                # Extração já completa
                arquivos_existem, folder_path, channel_file, videos_file = self.verificar_arquivos_existentes(channel_url)
                result = f"""
⏭️ ETAPA 1 JÁ CONCLUÍDA! (Arquivos encontrados)
📁 Pasta existente: {folder_path}
📄 Arquivo do canal: {os.path.basename(channel_file)}
📄 Arquivo de vídeos: {os.path.basename(videos_file)}
🎥 Total de vídeos encontrados: {videos_ja_extraidos}
"""
                print(result)
                return result
            else:
                # Há progresso parcial, mas pode extrair mais
                print(f"📊 Progresso parcial encontrado: {videos_ja_extraidos} vídeos já extraídos")
                print(f"🔄 Continuando extração para atingir {max_videos} vídeos...")

        # Verifica se os arquivos já existem completamente
        arquivos_existem, folder_path, channel_file, videos_file = self.verificar_arquivos_existentes(channel_url)

        if arquivos_existem and not tem_progresso:
            # Carrega dados existentes para contar vídeos
            try:
                with open(videos_file, 'r', encoding='utf-8') as f:
                    videos_data = json.load(f)

                result = f"""
⏭️ ETAPA 1 JÁ CONCLUÍDA! (Arquivos encontrados)
📁 Pasta existente: {folder_path}
📄 Arquivo do canal: {os.path.basename(channel_file)}
📄 Arquivo de vídeos: {os.path.basename(videos_file)}
🎥 Total de vídeos encontrados: {len(videos_data)}
"""
                print(result)
                return result

            except Exception as e:
                print(f"⚠️ Erro ao ler arquivos existentes: {e}")
                print("🔄 Executando extração novamente...")

        # Se não existem ou houve erro, executa a extração
        channel_data, videos_data = extract_channel_and_videos(channel_url, max_videos)

        if not channel_data:
            return "❌ Falha na extração."

        if not videos_data:
            return "⚠️ Nenhum vídeo longo foi encontrado."

        # Salva dados
        folder_path, channel_file, videos_file = save_data(channel_data, videos_data)

        # Relatório
        result = f"""
✅ ETAPA 1 CONCLUÍDA!
📁 Pasta criada: {folder_path}
📄 Arquivo do canal: {os.path.basename(channel_file)}
📄 Arquivo de vídeos: {os.path.basename(videos_file)}
🎥 Total de vídeos extraídos: {len(videos_data)}
"""

        print(result)
        return result

if __name__ == "__main__":
    main()
