# 🧠 Gerador de Campo Semântico da Persona

Este script automatiza a criação do Campo Semântico da Persona a partir dos comentários analisados, seguin<PERSON> as especificações do arquivo `instrucoes_para_gerar_persona.md`.

## 📋 Funcionalidades

O script executa as seguintes etapas:

1. **Carregamento do JSON** - Lê o arquivo de comentários analisados
2. **Filtragem** - Separa apenas comentários úteis (`comentario_util: true`)
3. **Agrupamento e Contagem** - Usa `collections.Counter` para contar frequências
4. **Geração de Blocos** - Cria tops 100 para cada categoria
5. **Exportação** - Gera documentos em múltiplos formatos

## 🚀 Como Usar

### 1. Instalar Dependências

```bash
pip install python-docx
```

Ou instalar todas as dependências:

```bash
pip install -r requirements.txt
```

### 2. Executar o Script

```bash
python gerador_persona.py
```

O script irá:
- Carregar automaticamente o arquivo `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/analises/comentarios.json`
- Processar todos os comentários úteis
- Criar automaticamente a pasta `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/` para organizar os arquivos
- Gerar os documentos de saída na pasta criada

## 📁 Arquivos Gerados

Todos os arquivos são salvos na pasta `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/` para manter a organização:

### Documento Principal
- `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/campo_semantico_persona.md` - Documento completo em Markdown
- `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/campo_semantico_persona.txt` - Versão em texto simples
- `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/campo_semantico_persona.docx` - Documento Word formatado (se python-docx estiver instalado)

### Dados Estruturados
Para cada categoria, são gerados arquivos separados:
- `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/dados_persona_categorias.txt/.docx`
- `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/dados_persona_medos.txt/.docx`
- `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/dados_persona_dificuldades.txt/.docx`
- `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/dados_persona_valores.txt/.docx`
- `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/dados_persona_sonhos.txt/.docx`
- `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/dados_persona_emocoes.txt/.docx`
- `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/dados_persona_linguagem.txt/.docx`
- `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/dados_persona_ideias_conteudo.txt/.docx`
- `Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona/dados_persona_oportunidades_negocio.txt/.docx`

## 📊 Estrutura do Documento Principal

O documento gerado contém as seguintes seções:

1. **🏷️ Top 100 Categorias Mais Citadas**
2. **😰 Top 100 Medos Mais Frequentes**
3. **⚠️ Top 100 Dificuldades Mais Relatadas**
4. **💎 Top 100 Valores Mais Importantes**
5. **✨ Top 100 Sonhos e Aspirações**
6. **😊 Frequência das Emoções Principais**
7. **💬 Top 100 Expressões Reais Mais Frequentes**
8. **💡 Top 100 Ideias de Conteúdo**
9. **💰 Top 100 Oportunidades de Produtos Digitais**
10. **📄 100 Resumos Mais Representativos**

## 🔧 Configurações

### Alterar Arquivo de Entrada

Para usar um arquivo diferente, modifique a variável `caminho_comentarios` na função `main()`:

```python
caminho_comentarios = "seu/caminho/para/comentarios.json"
```

### Alterar Quantidade de Itens nos Tops

Para alterar o número de itens nos tops (padrão: 100), modifique as chamadas da função `gerar_bloco_top_n()`:

```python
blocos.append(gerar_bloco_top_n(contadores['medos'], "😰 Top 50 Medos", 50))
```

## 📈 Estatísticas Exibidas

Durante a execução, o script exibe:
- Tamanho do arquivo carregado
- Total de comentários no arquivo
- Quantidade de comentários úteis
- Percentual de comentários úteis
- Número de itens únicos por categoria
- Lista de arquivos gerados
- Resumo final com estatísticas

## ⚠️ Requisitos do Arquivo JSON

O arquivo de entrada deve ter a seguinte estrutura:

```json
[
  {
    "comentario_util": true,
    "categoria": "string",
    "medos": ["string1", "string2"],
    "dificuldades": ["string1", "string2"],
    "valores": ["string1", "string2"],
    "sonhos": ["string1", "string2"],
    "emocao_principal": "string",
    "linguagem": ["string1", "string2"],
    "motivacao": "string",
    "resumo": "string",
    "ideias_conteudo": ["string1", "string2"],
    "oportunidades_negocio": ["string1", "string2"]
  }
]
```

## 🐛 Tratamento de Erros

O script inclui tratamento para:
- Arquivo não encontrado
- Arquivo muito grande (>500MB)
- Formato JSON inválido
- Campos ausentes ou vazios
- Problemas de codificação UTF-8

## 💡 Dicas de Uso

1. **Backup**: Faça backup dos arquivos originais antes de executar
2. **Espaço em Disco**: Verifique se há espaço suficiente para os arquivos de saída
3. **Memória**: Para arquivos muito grandes, considere usar `ijson` para leitura incremental
4. **Encoding**: Certifique-se de que o arquivo JSON está em UTF-8

## 🔄 Atualizações Futuras

Possíveis melhorias:
- Suporte a múltiplos arquivos de entrada
- Filtros personalizáveis
- Exportação para outros formatos (PDF, Excel)
- Interface gráfica
- Análise de sentimentos
- Visualizações gráficas
