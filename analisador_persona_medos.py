#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para análise estratégica dos medos da persona usando IA
Processa dados em lotes para respeitar limites da API OpenAI
"""

import os
import json
from datetime import datetime
from openai import OpenAI
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

class AnalisadorPersonaMedos:
    def __init__(self):
        self.client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.pasta_saida = "Mayra Gaiato _ Desenvolvimento Infantil e Autismo/persona"
        
    def carregar_prompt_base(self):
        """Carrega o prompt base do arquivo"""
        try:
            with open('prompt_analise_persona_medos.md', 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            print("❌ Arquivo prompt_analise_persona_medos.md não encontrado!")
            return None
    
    def carregar_dados_medos(self):
        """Carrega os dados de medos do arquivo gerado"""
        arquivo_medos = os.path.join(self.pasta_saida, "dados_persona_medos.txt")
        
        if not os.path.exists(arquivo_medos):
            print(f"❌ Arquivo não encontrado: {arquivo_medos}")
            return None
        
        medos = []
        with open(arquivo_medos, 'r', encoding='utf-8') as f:
            for linha in f:
                linha = linha.strip()
                if linha and not linha.startswith('#') and ':' in linha:
                    medos.append(linha)
        
        print(f"📊 Total de medos carregados: {len(medos)}")
        return medos
    
    def dividir_em_lotes(self, medos, tamanho_lote=500):
        """Divide os medos em lotes para processamento"""
        lotes = []
        for i in range(0, len(medos), tamanho_lote):
            lote = medos[i:i + tamanho_lote]
            lotes.append(lote)

        print(f"📦 Dados divididos em {len(lotes)} lotes de até {tamanho_lote} itens")
        print(f"💡 Com 1830 medos, teremos aproximadamente {len(lotes)} lotes para processar")
        return lotes
    
    def processar_lote(self, lote, numero_lote, total_lotes, prompt_base):
        """Processa um lote de medos com a IA"""
        print(f"🤖 Processando lote {numero_lote}/{total_lotes}...")
        
        # Preparar dados do lote
        dados_lote = "\n".join(lote)
        
        # Criar prompt específico para o lote
        prompt_lote = f"""
{prompt_base}

## 📊 DADOS PARA ANÁLISE (LOTE {numero_lote}/{total_lotes})

```
{dados_lote}
```

IMPORTANTE: Este é o lote {numero_lote} de {total_lotes}. Analise apenas estes dados e forneça insights específicos para este conjunto.
"""
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",  # Modelo mais econômico
                messages=[
                    {
                        "role": "system",
                        "content": "Você é uma estrategista de marketing especializada em análise de personas e copywriting para públicos vulneráveis."
                    },
                    {
                        "role": "user",
                        "content": prompt_lote
                    }
                ],
                max_tokens=8000,  # Aumentado para aproveitar melhor a API
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            print(f"❌ Erro ao processar lote {numero_lote}: {e}")
            return None
    
    def consolidar_analises(self, analises_lotes):
        """Consolida as análises dos lotes em um documento final"""
        print("🔄 Consolidando análises...")
        
        prompt_consolidacao = f"""
Você recebeu {len(analises_lotes)} análises parciais de medos de pais/mães de crianças autistas.

Sua missão é consolidar essas análises em um documento estratégico final único e coerente.

INSTRUÇÕES:
1. Combine os temas similares das diferentes análises
2. Crie uma visão unificada dos medos principais
3. Desenvolva estratégias integradas baseadas em todos os dados
4. Mantenha o formato markdown com emojis
5. Foque em insights acionáveis para marketing e produtos

ANÁLISES PARCIAIS:
"""
        
        for i, analise in enumerate(analises_lotes, 1):
            if analise:
                prompt_consolidacao += f"\n\n## ANÁLISE LOTE {i}:\n{analise}\n"
        
        try:
            response = self.client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {
                        "role": "system",
                        "content": "Você é uma estrategista sênior consolidando insights de múltiplas análises para criar uma estratégia unificada."
                    },
                    {
                        "role": "user",
                        "content": prompt_consolidacao
                    }
                ],
                max_tokens=16000,  # Aumentado significativamente para consolidação completa
                temperature=0.7
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            print(f"❌ Erro na consolidação: {e}")
            return None
    
    def salvar_resultado(self, conteudo, nome_arquivo="analise_estrategica_medos"):
        """Salva o resultado da análise"""
        if not conteudo:
            print("❌ Nenhum conteúdo para salvar!")
            return None
        
        # Adicionar cabeçalho com metadados
        timestamp = datetime.now().strftime("%d/%m/%Y às %H:%M")
        cabecalho = f"""# 🎯 ANÁLISE ESTRATÉGICA DOS MEDOS - PERSONA AUTISMO

**Gerado em:** {timestamp}
**Fonte:** dados_persona_medos.txt
**Método:** Análise por lotes com IA (GPT-4o-mini)

---

"""
        
        conteudo_final = cabecalho + conteudo
        
        # Salvar como Markdown
        arquivo_md = os.path.join(self.pasta_saida, f"{nome_arquivo}.md")
        with open(arquivo_md, 'w', encoding='utf-8') as f:
            f.write(conteudo_final)
        
        print(f"✅ Análise salva: {arquivo_md}")
        return arquivo_md
    
    def executar_analise_completa(self, tamanho_lote=500):
        """Executa a análise completa dos medos"""
        print("🚀 Iniciando análise estratégica dos medos...\n")
        
        # 1. Carregar prompt base
        prompt_base = self.carregar_prompt_base()
        if not prompt_base:
            return None
        
        # 2. Carregar dados de medos
        medos = self.carregar_dados_medos()
        if not medos:
            return None
        
        # 3. Dividir em lotes
        lotes = self.dividir_em_lotes(medos, tamanho_lote)
        
        # 4. Processar cada lote
        analises_lotes = []
        for i, lote in enumerate(lotes, 1):
            analise = self.processar_lote(lote, i, len(lotes), prompt_base)
            if analise:
                analises_lotes.append(analise)
                
                # Salvar análise do lote (backup)
                nome_lote = f"analise_medos_lote_{i}"
                arquivo_lote = os.path.join(self.pasta_saida, f"{nome_lote}.md")
                with open(arquivo_lote, 'w', encoding='utf-8') as f:
                    f.write(f"# Análise Lote {i}\n\n{analise}")
                print(f"💾 Backup do lote {i} salvo")
        
        if not analises_lotes:
            print("❌ Nenhuma análise foi processada com sucesso!")
            return None
        
        # 5. Consolidar análises
        analise_final = self.consolidar_analises(analises_lotes)
        
        # 6. Salvar resultado final
        arquivo_final = self.salvar_resultado(analise_final)
        
        print(f"\n🎉 Análise completa finalizada!")
        print(f"📁 Arquivo principal: {arquivo_final}")
        print(f"📊 Lotes processados: {len(analises_lotes)}/{len(lotes)}")
        
        return arquivo_final

def main():
    """Função principal"""
    analisador = AnalisadorPersonaMedos()
    
    # Verificar se a API key está configurada
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ OPENAI_API_KEY não encontrada no arquivo .env!")
        print("💡 Crie um arquivo .env com: OPENAI_API_KEY=sua_chave_aqui")
        return
    
    # Executar análise
    resultado = analisador.executar_analise_completa(tamanho_lote=500)  # Lotes otimizados
    
    if resultado:
        print(f"\n✅ Sucesso! Análise estratégica gerada em: {resultado}")
    else:
        print("\n❌ Falha na geração da análise!")

if __name__ == "__main__":
    main()
