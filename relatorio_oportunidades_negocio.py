#!/usr/bin/env python3
"""
Script para extrair oportunidades de negócio dos comentários úteis

Este script:
1. Lê o arquivo comentarios.json
2. Filtra comentários onde "comentario_util": true
3. Extrai o campo "oportunidades_negocio" 
4. Salva cada oportunidade em uma linha no arquivo relatorios/oportunidades_negocio.txt
"""

import json
import os
from datetime import datetime

def extract_business_opportunities(channel_folder):
    """Extrai oportunidades de negócio dos comentários úteis"""
    
    # Arquivo de entrada
    comments_file = os.path.join(channel_folder, 'analises', 'comentarios.json')
    
    if not os.path.exists(comments_file):
        print("❌ Arquivo de comentários não encontrado:")
        print(f"   📁 {comments_file}")
        print("💡 Execute a Etapa 4 (Análise de Comentários) primeiro!")
        return False
    
    print(f"📂 Carregando comentários de: {comments_file}")
    
    try:
        with open(comments_file, 'r', encoding='utf-8') as f:
            comments_data = json.load(f)
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo de comentários: {e}")
        return False
    
    print(f"📊 Total de comentários carregados: {len(comments_data)}")
    
    # Filtra comentários úteis
    useful_comments = [comment for comment in comments_data if comment.get('comentario_util', False)]
    print(f"✅ Comentários úteis encontrados: {len(useful_comments)}")
    
    if len(useful_comments) == 0:
        print("⚠️ Nenhum comentário útil encontrado!")
        return False
    
    # Extrai oportunidades de negócio
    all_opportunities = []
    comments_with_opportunities = 0
    
    for comment in useful_comments:
        opportunities = comment.get('oportunidades_negocio', [])
        
        if opportunities and len(opportunities) > 0:
            comments_with_opportunities += 1
            
            # Adiciona cada oportunidade à lista
            for opportunity in opportunities:
                if opportunity and opportunity.strip():  # Verifica se não está vazio
                    all_opportunities.append(opportunity.strip())
    
    print(f"💼 Comentários com oportunidades: {comments_with_opportunities}")
    print(f"🎯 Total de oportunidades extraídas: {len(all_opportunities)}")
    
    if len(all_opportunities) == 0:
        print("⚠️ Nenhuma oportunidade de negócio encontrada!")
        return False
    
    # Remove duplicatas mantendo a ordem
    unique_opportunities = []
    seen = set()
    
    for opportunity in all_opportunities:
        opportunity_lower = opportunity.lower()
        if opportunity_lower not in seen:
            seen.add(opportunity_lower)
            unique_opportunities.append(opportunity)
    
    print(f"🔄 Oportunidades únicas (sem duplicatas): {len(unique_opportunities)}")
    
    # Cria pasta de relatórios
    reports_folder = os.path.join(channel_folder, 'relatorios')
    os.makedirs(reports_folder, exist_ok=True)
    
    # Arquivo de saída com timestamp
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = os.path.join(reports_folder, f'oportunidades_negocio_{timestamp}.txt')

    # Também cria/atualiza arquivo principal
    main_output_file = os.path.join(reports_folder, 'oportunidades_negocio.txt')
    
    def write_report(file_path, include_header=True):
        """Escreve o relatório em um arquivo"""
        with open(file_path, 'w', encoding='utf-8') as f:
            if include_header:
                # Cabeçalho completo
                f.write("=" * 80 + "\n")
                f.write("RELATÓRIO DE OPORTUNIDADES DE NEGÓCIO\n")
                f.write("=" * 80 + "\n")
                f.write(f"Gerado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Fonte: {comments_file}\n")
                f.write(f"Total de comentários analisados: {len(comments_data)}\n")
                f.write(f"Comentários úteis: {len(useful_comments)}\n")
                f.write(f"Comentários com oportunidades: {comments_with_opportunities}\n")
                f.write(f"Oportunidades únicas extraídas: {len(unique_opportunities)}\n")
                f.write("=" * 80 + "\n\n")

            # Lista de oportunidades (uma por linha)
            for i, opportunity in enumerate(unique_opportunities, 1):
                f.write(f"{i:3d}. {opportunity}\n")

    try:
        # Salva arquivo com timestamp (histórico)
        write_report(output_file, include_header=True)
        print(f"💾 Relatório com timestamp salvo: {output_file}")

        # Salva arquivo principal (sempre atualizado)
        write_report(main_output_file, include_header=True)
        print(f"💾 Relatório principal atualizado: {main_output_file}")

        return True
        
    except Exception as e:
        print(f"❌ Erro ao salvar relatório: {e}")
        return False

def main():
    """Função principal"""
    print("🎯 EXTRAÇÃO DE OPORTUNIDADES DE NEGÓCIO")
    print("=" * 60)
    
    # Procura pela pasta do canal
    channel_folder = "Mayra Gaiato _ Desenvolvimento Infantil e Autismo"
    
    if not os.path.exists(channel_folder):
        print(f"❌ Pasta do canal não encontrada: {channel_folder}")
        print("💡 Execute as etapas anteriores primeiro")
        return
    
    print(f"📁 Pasta do canal: {channel_folder}")
    
    # Extrai oportunidades de negócio
    success = extract_business_opportunities(channel_folder)
    
    if success:
        print("\n🎉 EXTRAÇÃO CONCLUÍDA COM SUCESSO!")
        print("📋 Verifique o arquivo gerado na pasta 'relatorios'")
    else:
        print("\n❌ FALHA NA EXTRAÇÃO!")
        print("💡 Verifique se a Etapa 4 foi executada corretamente")

if __name__ == "__main__":
    main()
