#!/usr/bin/env python3
"""
Script para gerar relatório dos vídeos mais visualizados do canal

Este script:
1. Lê o arquivo 2_todosvideosdocanal.json
2. Ordena vídeos por número de visualizações (maior para menor)
3. Salva relatório em relatorios/videos_mais_visualizados.txt
4. Inclui estatísticas e análises dos dados
"""

import json
import os
from datetime import datetime

def format_number(num):
    """Formata números grandes de forma legível"""
    if num >= 1_000_000:
        return f"{num/1_000_000:.1f}M"
    elif num >= 1_000:
        return f"{num/1_000:.1f}K"
    else:
        return str(num)

def analyze_videos_by_views(channel_folder):
    """Analisa vídeos por visualizações"""
    
    # Arquivo de entrada
    videos_file = os.path.join(channel_folder, '2_todosvideosdocanal.json')
    
    if not os.path.exists(videos_file):
        print("❌ Arquivo de vídeos não encontrado:")
        print(f"   📁 {videos_file}")
        print("💡 Execute a Etapa 1 (Extração do Canal) primeiro!")
        return False
    
    print(f"📂 Carregando vídeos de: {videos_file}")
    
    try:
        with open(videos_file, 'r', encoding='utf-8') as f:
            videos_data = json.load(f)
    except Exception as e:
        print(f"❌ Erro ao carregar arquivo de vídeos: {e}")
        return False
    
    print(f"📊 Total de vídeos carregados: {len(videos_data)}")
    
    if len(videos_data) == 0:
        print("⚠️ Nenhum vídeo encontrado!")
        return False
    
    # Filtra vídeos que têm dados de visualizações
    videos_with_views = []
    videos_without_views = 0
    
    for video in videos_data:
        views = video.get('numero_visualizacoes')
        if views is not None and isinstance(views, (int, float)) and views >= 0:
            videos_with_views.append(video)
        else:
            videos_without_views += 1
    
    print(f"✅ Vídeos com dados de visualizações: {len(videos_with_views)}")
    print(f"⚠️ Vídeos sem dados de visualizações: {videos_without_views}")
    
    if len(videos_with_views) == 0:
        print("❌ Nenhum vídeo com dados de visualizações encontrado!")
        return False
    
    # Ordena por visualizações (maior para menor)
    videos_sorted = sorted(videos_with_views, key=lambda x: x.get('numero_visualizacoes', 0), reverse=True)

    # Calcula estatísticas
    total_views = sum(video.get('numero_visualizacoes', 0) for video in videos_sorted)
    avg_views = total_views / len(videos_sorted)
    median_index = len(videos_sorted) // 2
    median_views = videos_sorted[median_index].get('numero_visualizacoes', 0)

    top_10_views = sum(video.get('numero_visualizacoes', 0) for video in videos_sorted[:10])
    top_10_percentage = (top_10_views / total_views) * 100 if total_views > 0 else 0
    
    print(f"\n📊 ESTATÍSTICAS DE VISUALIZAÇÕES:")
    print(f"   📈 Total de visualizações: {format_number(total_views)} ({total_views:,})")
    print(f"   📊 Média de visualizações: {format_number(avg_views)} ({avg_views:,.0f})")
    print(f"   📊 Mediana de visualizações: {format_number(median_views)} ({median_views:,})")
    print(f"   🏆 Top 10 representam: {top_10_percentage:.1f}% das visualizações")
    
    # Mostra top 10
    print(f"\n🏆 TOP 10 VÍDEOS MAIS VISUALIZADOS:")
    for i, video in enumerate(videos_sorted[:10], 1):
        title = video.get('titulo', 'Título não disponível')[:60]
        views = video.get('numero_visualizacoes', 0)
        print(f"   {i:2d}. {format_number(views):>8s} - {title}...")
    
    # Cria pasta de relatórios
    reports_folder = os.path.join(channel_folder, 'relatorios')
    os.makedirs(reports_folder, exist_ok=True)
    
    # Arquivos de saída
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_file = os.path.join(reports_folder, f'videos_mais_visualizados_{timestamp}.txt')
    main_output_file = os.path.join(reports_folder, 'videos_mais_visualizados.txt')
    
    def save_report(file_path):
        """Salva o relatório em formato TXT"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                # Cabeçalho
                f.write("=" * 80 + "\n")
                f.write("RELATÓRIO DE VÍDEOS MAIS VISUALIZADOS\n")
                f.write("=" * 80 + "\n")
                f.write(f"Gerado em: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Fonte: {videos_file}\n")
                f.write(f"Total de vídeos: {len(videos_data)}\n")
                f.write(f"Vídeos com visualizações: {len(videos_with_views)}\n")
                f.write(f"Vídeos sem visualizações: {videos_without_views}\n")
                f.write(f"Total de visualizações: {format_number(total_views)} ({total_views:,})\n")
                f.write(f"Média de visualizações: {format_number(avg_views)} ({avg_views:,.0f})\n")
                f.write(f"Mediana de visualizações: {format_number(median_views)} ({median_views:,})\n")
                f.write(f"Top 10 representam: {top_10_percentage:.1f}% das visualizações\n")
                f.write("=" * 80 + "\n\n")

                # Lista de vídeos (uma por linha)
                for i, video in enumerate(videos_sorted, 1):
                    title = video.get('titulo', 'Título não disponível')
                    views = video.get('numero_visualizacoes', 0)
                    views_formatted = format_number(views)
                    data_upload = video.get('data_upload', 'N/A')

                    f.write(f"{i:3d}. ({views_formatted}) ({data_upload}) - {title}\n")

            return True
        except Exception as e:
            print(f"❌ Erro ao salvar {file_path}: {e}")
            return False
    
    # Salva arquivos
    success_count = 0
    
    if save_report(output_file):
        print(f"💾 Relatório com timestamp salvo: {output_file}")
        success_count += 1
    
    if save_report(main_output_file):
        print(f"💾 Relatório principal atualizado: {main_output_file}")
        success_count += 1
    
    return success_count > 0

def main():
    """Função principal"""
    print("🎯 RELATÓRIO DE VÍDEOS MAIS VISUALIZADOS")
    print("=" * 60)
    
    # Procura pela pasta do canal
    channel_folder = "Mayra Gaiato _ Desenvolvimento Infantil e Autismo"
    
    if not os.path.exists(channel_folder):
        print(f"❌ Pasta do canal não encontrada: {channel_folder}")
        print("💡 Execute as etapas anteriores primeiro")
        return
    
    print(f"📁 Pasta do canal: {channel_folder}")
    
    # Gera relatório de visualizações
    success = analyze_videos_by_views(channel_folder)
    
    if success:
        print("\n🎉 RELATÓRIO GERADO COM SUCESSO!")
        print("📋 Verifique o arquivo gerado na pasta 'relatorios'")
        print("📊 O ranking completo está ordenado do mais para o menos visualizado")
    else:
        print("\n❌ FALHA NA GERAÇÃO DO RELATÓRIO!")
        print("💡 Verifique se a Etapa 1 foi executada corretamente")

if __name__ == "__main__":
    main()
