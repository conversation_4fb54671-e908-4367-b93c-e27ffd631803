#!/usr/bin/env python3
"""
Monitor de Progresso para Análise de Comentários
Monitora o progresso da análise de comentários em tempo real
"""

import json
import os
import time
from datetime import datetime

def monitor_progress(channel_folder, interval=10):
    """
    Monitora o progresso da análise de comentários
    
    Args:
        channel_folder: Pasta do canal
        interval: Intervalo de verificação em segundos
    """
    
    analyses_file = os.path.join(channel_folder, 'analises', 'comentarios.json')
    videos_file = os.path.join(channel_folder, 'analises', 'videos.json')
    
    print("🔍 MONITOR DE PROGRESSO - Análise de Comentários")
    print("=" * 60)
    print(f"📁 Canal: {channel_folder}")
    print(f"⏱️ Intervalo: {interval}s")
    print("=" * 60)
    
    # Carrega informações dos vídeos relevantes
    total_comments_to_analyze = 0
    relevant_videos = []
    
    if os.path.exists(videos_file):
        try:
            with open(videos_file, 'r', encoding='utf-8') as f:
                all_videos = json.load(f)
            
            relevant_videos = [v for v in all_videos if v.get('relevante', False)]
            
            # Conta total de comentários a analisar
            for video in relevant_videos:
                video_id = video.get('id_video')
                comments_file = os.path.join(channel_folder, 'comentarios', f"{video_id}_comentarios.json")
                
                if os.path.exists(comments_file):
                    try:
                        with open(comments_file, 'r', encoding='utf-8') as f:
                            comments_data = json.load(f)
                        total_comments_to_analyze += len(comments_data.get('comentarios', []))
                    except:
                        pass
            
            print(f"📹 Vídeos relevantes: {len(relevant_videos)}")
            print(f"💬 Total de comentários para analisar: {total_comments_to_analyze}")
            print("=" * 60)
            
        except Exception as e:
            print(f"⚠️ Erro ao carregar dados dos vídeos: {e}")
            return
    
    last_count = 0
    start_time = time.time()
    
    try:
        while True:
            current_time = datetime.now().strftime("%H:%M:%S")
            
            # Verifica análises atuais
            current_count = 0
            if os.path.exists(analyses_file):
                try:
                    with open(analyses_file, 'r', encoding='utf-8') as f:
                        analyses = json.load(f)
                    current_count = len(analyses)
                except:
                    pass
            
            # Calcula estatísticas
            elapsed = time.time() - start_time
            progress_pct = (current_count / total_comments_to_analyze * 100) if total_comments_to_analyze > 0 else 0
            new_analyses = current_count - last_count
            
            if elapsed > 0:
                rate = current_count / elapsed
                if rate > 0:
                    eta_seconds = (total_comments_to_analyze - current_count) / rate
                    eta_minutes = eta_seconds / 60
                else:
                    eta_minutes = 0
            else:
                rate = 0
                eta_minutes = 0
            
            # Exibe status
            print(f"\r[{current_time}] 📊 {current_count}/{total_comments_to_analyze} ({progress_pct:.1f}%) | "
                  f"🚀 {rate:.1f}/s | ⏱️ ETA: {eta_minutes:.1f}min | 🆕 +{new_analyses}", end="", flush=True)
            
            last_count = current_count
            
            # Para se completou
            if current_count >= total_comments_to_analyze and total_comments_to_analyze > 0:
                print(f"\n✅ Análise concluída! {current_count} comentários processados em {elapsed/60:.1f} minutos")
                break
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print(f"\n\n⏹️ Monitoramento interrompido pelo usuário")
        print(f"📊 Status final: {current_count}/{total_comments_to_analyze} comentários analisados")

def show_detailed_progress(channel_folder):
    """Mostra progresso detalhado por vídeo"""
    
    analyses_file = os.path.join(channel_folder, 'analises', 'comentarios.json')
    videos_file = os.path.join(channel_folder, 'analises', 'videos.json')
    
    print("📊 PROGRESSO DETALHADO POR VÍDEO")
    print("=" * 80)
    
    # Carrega análises existentes
    analyzed_by_video = {}
    if os.path.exists(analyses_file):
        try:
            with open(analyses_file, 'r', encoding='utf-8') as f:
                analyses = json.load(f)
            
            for analysis in analyses:
                video_id = analysis.get('id_video')
                if video_id:
                    analyzed_by_video[video_id] = analyzed_by_video.get(video_id, 0) + 1
        except Exception as e:
            print(f"⚠️ Erro ao carregar análises: {e}")
            return
    
    # Carrega vídeos relevantes
    if os.path.exists(videos_file):
        try:
            with open(videos_file, 'r', encoding='utf-8') as f:
                all_videos = json.load(f)
            
            relevant_videos = [v for v in all_videos if v.get('relevante', False)]
            
            for i, video in enumerate(relevant_videos):
                video_id = video.get('id_video')
                video_title = video.get('titulo_video', 'Sem título')[:50]
                
                # Conta comentários totais
                comments_file = os.path.join(channel_folder, 'comentarios', f"{video_id}_comentarios.json")
                total_comments = 0
                
                if os.path.exists(comments_file):
                    try:
                        with open(comments_file, 'r', encoding='utf-8') as f:
                            comments_data = json.load(f)
                        total_comments = len(comments_data.get('comentarios', []))
                    except:
                        pass
                
                analyzed_count = analyzed_by_video.get(video_id, 0)
                progress_pct = (analyzed_count / total_comments * 100) if total_comments > 0 else 0
                
                status = "✅" if analyzed_count >= total_comments else "🔄" if analyzed_count > 0 else "⏳"
                
                print(f"{status} {i+1:2d}. {video_title}...")
                print(f"     📊 {analyzed_count}/{total_comments} ({progress_pct:.1f}%)")
                print()
                
        except Exception as e:
            print(f"⚠️ Erro ao carregar vídeos: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) < 2:
        print("Uso: python monitor_progresso.py <pasta_do_canal> [comando]")
        print("Comandos:")
        print("  monitor  - Monitora progresso em tempo real (padrão)")
        print("  status   - Mostra status detalhado por vídeo")
        sys.exit(1)
    
    channel_folder = sys.argv[1]
    command = sys.argv[2] if len(sys.argv) > 2 else "monitor"
    
    if not os.path.exists(channel_folder):
        print(f"❌ Pasta não encontrada: {channel_folder}")
        sys.exit(1)
    
    if command == "status":
        show_detailed_progress(channel_folder)
    else:
        monitor_progress(channel_folder)
